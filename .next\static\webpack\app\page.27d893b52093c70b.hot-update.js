"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        audioContext: null,\n        gainNode: null,\n        analyserNode: null,\n        eqNodes: [],\n        isInitialized: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    });\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const oscillatorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fréquences de l'égaliseur\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                console.log('Initializing audio context...');\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Reprendre le contexte si suspendu\n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully', {\n                    state: audioContext.state,\n                    sampleRate: audioContext.sampleRate\n                });\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], [\n        state.isInitialized\n    ]);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            console.log('Setting volume to:', volume);\n            if (!state.gainNode || !state.audioContext) {\n                console.log('No gain node or audio context available');\n                return;\n            }\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            try {\n                state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n                console.log('Volume set successfully:', gainValue);\n            } catch (error) {\n                console.error('Error setting volume:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            console.log('Setting EQ frequency:', freqKey, 'to gain:', gain);\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex] || !state.audioContext) {\n                console.log('EQ node not found or audio context not available');\n                return;\n            }\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            try {\n                eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n                console.log('EQ frequency set successfully:', freqKey, clampedGain);\n            } catch (error) {\n                console.error('Error setting EQ frequency:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    const playTestTone = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[playTestTone]\": async ()=>{\n            console.log('Playing test tone...');\n            if (!state.audioContext || !state.gainNode) {\n                console.log('Initializing audio context first...');\n                await initializeAudioContext();\n                return;\n            }\n            try {\n                // Arrêter l'oscillateur précédent s'il existe\n                if (oscillatorRef.current) {\n                    oscillatorRef.current.stop();\n                    oscillatorRef.current = null;\n                }\n                // Créer un nouvel oscillateur\n                const oscillator = state.audioContext.createOscillator();\n                oscillator.type = 'sine';\n                oscillator.frequency.setValueAtTime(440, state.audioContext.currentTime) // La note A4\n                ;\n                // Connecter l'oscillateur à la chaîne audio\n                oscillator.connect(state.gainNode);\n                // Démarrer et arrêter après 2 secondes\n                oscillator.start();\n                oscillator.stop(state.audioContext.currentTime + 2);\n                oscillatorRef.current = oscillator;\n                console.log('Test tone playing for 2 seconds');\n            } catch (error) {\n                console.error('Error playing test tone:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[playTestTone]\"], [\n        state.audioContext,\n        state.gainNode,\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData,\n        playTestTone\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});