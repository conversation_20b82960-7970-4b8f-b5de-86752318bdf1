"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        audioContext: null,\n        gainNode: null,\n        analyserNode: null,\n        eqNodes: [],\n        isInitialized: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    });\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const oscillatorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fréquences de l'égaliseur\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            try {\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully');\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], []);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            if (!state.gainNode) return;\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex]) return;\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    // Initialiser automatiquement au montage\n    useEffect({\n        \"useAudioContext.useEffect\": ()=>{\n            initializeAudioContext();\n        }\n    }[\"useAudioContext.useEffect\"], [\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});