'use client'

import { useRef, useEffect, useState } from 'react'

interface AudioPlayerProps {
  onAudioElementReady: (audioElement: HTMLAudioElement) => void
}

export default function AudioPlayer({ onAudioElementReady }: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [currentTrack, setCurrentTrack] = useState({
    title: 'Test Audio',
    artist: 'WaveCraft',
    url: ''
  })

  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    // Notifier le parent que l'élément audio est prêt
    onAudioElementReady(audio)

    const handleTimeUpdate = () => setCurrentTime(audio.currentTime)
    const handleDurationChange = () => setDuration(audio.duration)
    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)

    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('durationchange', handleDurationChange)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('durationchange', handleDurationChange)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
    }
  }, [onAudioElementReady])

  const togglePlayPause = () => {
    const audio = audioRef.current
    if (!audio) return

    if (isPlaying) {
      audio.pause()
    } else {
      audio.play().catch(console.error)
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current
    if (!audio) return

    const newTime = parseFloat(e.target.value)
    audio.currentTime = newTime
    setCurrentTime(newTime)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const loadTestAudio = () => {
    // Créer un oscillateur pour générer un son de test
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)
    
    oscillator.frequency.setValueAtTime(440, audioContext.currentTime) // La note A4
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
    
    oscillator.start()
    
    setTimeout(() => {
      oscillator.stop()
      audioContext.close()
    }, 2000)
  }

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-white mb-4">Lecteur Audio</h3>
      
      {/* Info du morceau */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-12 h-12 bg-blue-600 rounded flex items-center justify-center">
          <span className="text-xl">🎵</span>
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-white">{currentTrack.title}</h4>
          <p className="text-gray-400 text-sm">{currentTrack.artist}</p>
        </div>
      </div>

      {/* Contrôles de lecture */}
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={togglePlayPause}
            className="w-12 h-12 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center text-white"
          >
            {isPlaying ? '⏸️' : '▶️'}
          </button>
          
          <button
            onClick={loadTestAudio}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white text-sm"
          >
            🔊 Test Son
          </button>
        </div>

        {/* Barre de progression */}
        <div className="space-y-2">
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            className="w-full h-2 bg-gray-700 rounded appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
      </div>

      {/* Élément audio caché */}
      <audio
        ref={audioRef}
        crossOrigin="anonymous"
        preload="metadata"
      >
        Votre navigateur ne supporte pas l'élément audio.
      </audio>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-gray-700 rounded">
        <p className="text-sm text-gray-300">
          💡 <strong>Instructions :</strong>
        </p>
        <ul className="text-xs text-gray-400 mt-1 space-y-1">
          <li>• Cliquez sur "Test Son" pour générer un son de test</li>
          <li>• Utilisez l'égaliseur ci-dessous pour modifier le son</li>
          <li>• Ajustez le volume principal</li>
        </ul>
      </div>
    </div>
  )
}
