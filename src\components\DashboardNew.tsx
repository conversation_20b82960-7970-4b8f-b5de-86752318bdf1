'use client'

import { useState, useEffect, useCallback } from 'react'
import AudioVisualizerNew from './AudioVisualizerNew'
import QuickStats from './QuickStats'
import { useAudioEngine } from '@/hooks/useAudioEngine'

export default function DashboardNew() {
  const { getAnalyserData, isInitialized, isPlaying, initializeAudio } = useAudioEngine()
  
  const [currentTrack, setCurrentTrack] = useState({
    title: 'Aucune musique détectée',
    artist: 'En attente...',
    genre: 'Inconnu',
    confidence: 0
  })

  const [isListening, setIsListening] = useState(true)

  const tracks = [
    { title: 'Bohemian Rhapsody', artist: 'Queen', genre: 'Rock', confidence: 95 },
    { title: '<PERSON>', artist: '<PERSON>', genre: 'Pop', confidence: 92 },
    { title: 'Hotel California', artist: 'Eagles', genre: 'Rock', confidence: 88 },
    { title: 'Imagine', artist: '<PERSON>', genre: 'Folk', confidence: 90 }
  ]

  const updateTrack = useCallback(() => {
    if (isListening) {
      const randomTrack = tracks[Math.floor(Math.random() * tracks.length)]
      setCurrentTrack(randomTrack)
    }
  }, [isListening])

  // Simulation de détection audio
  useEffect(() => {
    const interval = setInterval(updateTrack, 4000)
    return () => clearInterval(interval)
  }, [updateTrack])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-gray-400">Vue d'ensemble de votre expérience audio</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsListening(!isListening)}
            className={`px-6 py-3 rounded font-medium ${
              isListening 
                ? 'bg-red-600 hover:bg-red-700 text-white' 
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {isListening ? '⏸️ Arrêter l\'écoute' : '▶️ Démarrer l\'écoute'}
          </button>
          
          {!isInitialized && (
            <button
              onClick={initializeAudio}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm"
            >
              Initialiser Audio
            </button>
          )}
        </div>
      </div>

      {/* Audio Status */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-2xl">🎵</span>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-white">{currentTrack.title}</h3>
            <p className="text-gray-400">{currentTrack.artist}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                {currentTrack.genre}
              </span>
              <span className="text-sm text-gray-400">
                Confiance: {currentTrack.confidence}%
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className={`w-3 h-3 rounded-full ${isListening ? 'bg-green-500' : 'bg-gray-500'}`}></div>
            <p className="text-xs text-gray-400 mt-1">
              {isListening ? 'En écoute' : 'Arrêté'}
            </p>
          </div>
        </div>
      </div>

      {/* Audio Engine Status */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Statut Audio Engine</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isInitialized ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <div>
              <p className="text-white font-medium">Contexte Audio</p>
              <p className="text-gray-400 text-sm">{isInitialized ? 'Initialisé' : 'Non initialisé'}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isPlaying ? 'bg-blue-500' : 'bg-gray-500'}`}></div>
            <div>
              <p className="text-white font-medium">Lecture</p>
              <p className="text-gray-400 text-sm">{isPlaying ? 'En cours' : 'Arrêté'}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isListening ? 'bg-yellow-500' : 'bg-gray-500'}`}></div>
            <div>
              <p className="text-white font-medium">Détection</p>
              <p className="text-gray-400 text-sm">{isListening ? 'Active' : 'Inactive'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Audio Visualizer */}
      <AudioVisualizerNew 
        isActive={isListening && isInitialized} 
        getAnalyserData={getAnalyserData}
        title="Visualisation Audio en Temps Réel"
      />

      {/* Quick Stats */}
      <QuickStats />

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Activité Récente</h3>
        <div className="space-y-3">
          {[
            { time: '14:32', action: 'Égaliseur ajusté automatiquement', track: 'Bohemian Rhapsody', type: 'eq' },
            { time: '14:28', action: 'Nouveau genre détecté: Rock', track: 'Hotel California', type: 'detection' },
            { time: '14:25', action: 'Préférences mises à jour', track: 'Billie Jean', type: 'settings' },
            { time: '14:20', action: 'Session d\'écoute démarrée', track: '-', type: 'session' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-700/50 rounded">
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'eq' ? 'bg-blue-500' :
                  activity.type === 'detection' ? 'bg-green-500' :
                  activity.type === 'settings' ? 'bg-yellow-500' :
                  'bg-purple-500'
                }`}></div>
                <div>
                  <p className="text-white text-sm">{activity.action}</p>
                  <p className="text-gray-400 text-xs">{activity.track}</p>
                </div>
              </div>
              <span className="text-gray-400 text-xs">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Actions Rapides</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 bg-blue-600/10 border border-blue-600/20 rounded hover:bg-blue-600/20">
            <div className="text-center">
              <span className="text-2xl mb-2 block">🎛️</span>
              <p className="text-white text-sm font-medium">Égaliseur</p>
            </div>
          </button>
          
          <button className="p-4 bg-green-600/10 border border-green-600/20 rounded hover:bg-green-600/20">
            <div className="text-center">
              <span className="text-2xl mb-2 block">🧠</span>
              <p className="text-white text-sm font-medium">Analyse IA</p>
            </div>
          </button>
          
          <button className="p-4 bg-purple-600/10 border border-purple-600/20 rounded hover:bg-purple-600/20">
            <div className="text-center">
              <span className="text-2xl mb-2 block">📊</span>
              <p className="text-white text-sm font-medium">Historique</p>
            </div>
          </button>
          
          <button className="p-4 bg-orange-600/10 border border-orange-600/20 rounded hover:bg-orange-600/20">
            <div className="text-center">
              <span className="text-2xl mb-2 block">⚙️</span>
              <p className="text-white text-sm font-medium">Paramètres</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
