"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardNew */ \"(app-pages-browser)/./src/components/DashboardNew.tsx\");\n/* harmony import */ var _components_EqualizerNew__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/EqualizerNew */ \"(app-pages-browser)/./src/components/EqualizerNew.tsx\");\n/* harmony import */ var _components_AIAnalysis__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AIAnalysis */ \"(app-pages-browser)/./src/components/AIAnalysis.tsx\");\n/* harmony import */ var _components_History__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/History */ \"(app-pages-browser)/./src/components/History.tsx\");\n/* harmony import */ var _components_Settings__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _components_Learning__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Learning */ \"(app-pages-browser)/./src/components/Learning.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n        }\n    }[\"Home.useCallback[handleTabChange]\"], []);\n    const renderContent = ()=>{\n        switch(activeTab){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 16\n                }, this);\n            case 'equalizer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EqualizerNew__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 16\n                }, this);\n            case 'ai-analysis':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAnalysis__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 16\n                }, this);\n            case 'history':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_History__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Settings__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, this);\n            case 'learning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Learning__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                activeTab: activeTab,\n                setActiveTab: handleTabChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: renderContent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"nYYhcld3/ncl/1UkEalOJyXGeRY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUU2QztBQUNIO0FBQ1U7QUFDQTtBQUNKO0FBQ047QUFDRTtBQUNBO0FBRTdCLFNBQVNTOztJQUN0QixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1gsK0NBQVFBLENBQUM7SUFFM0MsTUFBTVksa0JBQWtCWCxrREFBV0E7NkNBQUMsQ0FBQ1k7WUFDbkNGLGFBQWFFO1FBQ2Y7NENBQUcsRUFBRTtJQUVMLE1BQU1DLGdCQUFnQjtRQUNwQixPQUFRSjtZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNQLGdFQUFZQTs7Ozs7WUFDdEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0MsZ0VBQVlBOzs7OztZQUN0QixLQUFLO2dCQUNILHFCQUFPLDhEQUFDQyw4REFBVUE7Ozs7O1lBQ3BCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNDLDJEQUFPQTs7Ozs7WUFDakIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0MsNERBQVFBOzs7OztZQUNsQixLQUFLO2dCQUNILHFCQUFPLDhEQUFDQyw0REFBUUE7Ozs7O1lBQ2xCO2dCQUNFLHFCQUFPLDhEQUFDTCxnRUFBWUE7Ozs7O1FBQ3hCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1k7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNkLDJEQUFPQTtnQkFBQ1EsV0FBV0E7Z0JBQVdDLGNBQWNDOzs7Ozs7MEJBQzdDLDhEQUFDSztnQkFBS0QsV0FBVTswQkFDZCw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pGOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYO0dBcEN3Qkw7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWxhIEthbGJvdXNzaVxcRGVza3RvcFxcRXZlcnlcXGZwXFxlZ2FsaXNldXJcXHNyY1xcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgU2lkZWJhciBmcm9tICdAL2NvbXBvbmVudHMvU2lkZWJhcidcbmltcG9ydCBEYXNoYm9hcmROZXcgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZE5ldydcbmltcG9ydCBFcXVhbGl6ZXJOZXcgZnJvbSAnQC9jb21wb25lbnRzL0VxdWFsaXplck5ldydcbmltcG9ydCBBSUFuYWx5c2lzIGZyb20gJ0AvY29tcG9uZW50cy9BSUFuYWx5c2lzJ1xuaW1wb3J0IEhpc3RvcnkgZnJvbSAnQC9jb21wb25lbnRzL0hpc3RvcnknXG5pbXBvcnQgU2V0dGluZ3MgZnJvbSAnQC9jb21wb25lbnRzL1NldHRpbmdzJ1xuaW1wb3J0IExlYXJuaW5nIGZyb20gJ0AvY29tcG9uZW50cy9MZWFybmluZydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdkYXNoYm9hcmQnKVxuXG4gIGNvbnN0IGhhbmRsZVRhYkNoYW5nZSA9IHVzZUNhbGxiYWNrKCh0YWI6IHN0cmluZykgPT4ge1xuICAgIHNldEFjdGl2ZVRhYih0YWIpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHJlbmRlckNvbnRlbnQgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChhY3RpdmVUYWIpIHtcbiAgICAgIGNhc2UgJ2Rhc2hib2FyZCc6XG4gICAgICAgIHJldHVybiA8RGFzaGJvYXJkTmV3IC8+XG4gICAgICBjYXNlICdlcXVhbGl6ZXInOlxuICAgICAgICByZXR1cm4gPEVxdWFsaXplck5ldyAvPlxuICAgICAgY2FzZSAnYWktYW5hbHlzaXMnOlxuICAgICAgICByZXR1cm4gPEFJQW5hbHlzaXMgLz5cbiAgICAgIGNhc2UgJ2hpc3RvcnknOlxuICAgICAgICByZXR1cm4gPEhpc3RvcnkgLz5cbiAgICAgIGNhc2UgJ3NldHRpbmdzJzpcbiAgICAgICAgcmV0dXJuIDxTZXR0aW5ncyAvPlxuICAgICAgY2FzZSAnbGVhcm5pbmcnOlxuICAgICAgICByZXR1cm4gPExlYXJuaW5nIC8+XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPERhc2hib2FyZE5ldyAvPlxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgPFNpZGViYXIgYWN0aXZlVGFiPXthY3RpdmVUYWJ9IHNldEFjdGl2ZVRhYj17aGFuZGxlVGFiQ2hhbmdlfSAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgIHtyZW5kZXJDb250ZW50KCl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsIlNpZGViYXIiLCJEYXNoYm9hcmROZXciLCJFcXVhbGl6ZXJOZXciLCJBSUFuYWx5c2lzIiwiSGlzdG9yeSIsIlNldHRpbmdzIiwiTGVhcm5pbmciLCJIb21lIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaGFuZGxlVGFiQ2hhbmdlIiwidGFiIiwicmVuZGVyQ29udGVudCIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AudioVisualizerNew.tsx":
/*!***********************************************!*\
  !*** ./src/components/AudioVisualizerNew.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioVisualizerNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AudioVisualizerNew(param) {\n    let { isActive, getAnalyserData, title = \"Visualisation Audio\" } = param;\n    _s();\n    const [bars, setBars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(10).fill(0));\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioVisualizerNew.useEffect\": ()=>{\n            const updateBars = {\n                \"AudioVisualizerNew.useEffect.updateBars\": ()=>{\n                    if (isActive && getAnalyserData) {\n                        const dataArray = getAnalyserData();\n                        if (dataArray.length > 0) {\n                            // Prendre 10 échantillons répartis sur le spectre\n                            const newBars = [];\n                            const step = Math.floor(dataArray.length / 10);\n                            for(let i = 0; i < 10; i++){\n                                const index = i * step;\n                                const value = dataArray[index] || 0;\n                                // Convertir de 0-255 à 0-100\n                                newBars.push(value / 255 * 100);\n                            }\n                            setBars(newBars);\n                        } else {\n                            // Fallback vers simulation si pas de données\n                            setBars({\n                                \"AudioVisualizerNew.useEffect.updateBars\": (prev)=>prev.map({\n                                        \"AudioVisualizerNew.useEffect.updateBars\": ()=>Math.random() * 80 + 10\n                                    }[\"AudioVisualizerNew.useEffect.updateBars\"])\n                            }[\"AudioVisualizerNew.useEffect.updateBars\"]);\n                        }\n                    } else {\n                        setBars(new Array(10).fill(0));\n                    }\n                    if (isActive) {\n                        animationRef.current = requestAnimationFrame(updateBars);\n                    }\n                }\n            }[\"AudioVisualizerNew.useEffect.updateBars\"];\n            if (isActive) {\n                updateBars();\n            } else {\n                setBars(new Array(10).fill(0));\n            }\n            return ({\n                \"AudioVisualizerNew.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                }\n            })[\"AudioVisualizerNew.useEffect\"];\n        }\n    }[\"AudioVisualizerNew.useEffect\"], [\n        isActive,\n        getAnalyserData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-white mb-4\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizerNew.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-32 flex items-end justify-center space-x-2 bg-gray-800 rounded p-4\",\n                children: bars.map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 bg-blue-500 rounded-t\",\n                        style: {\n                            height: \"\".concat(Math.max(height, 5), \"%\")\n                        }\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizerNew.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizerNew.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-xs text-gray-400 text-center\",\n                children: isActive ? 'Analyse audio en temps réel' : 'Aucune activité audio'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizerNew.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizerNew.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioVisualizerNew, \"EJ5bYlVKxMyIa6+i7EDrM58HrP0=\");\n_c = AudioVisualizerNew;\nvar _c;\n$RefreshReg$(_c, \"AudioVisualizerNew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioVisualizerNew.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DashboardNew.tsx":
/*!*****************************************!*\
  !*** ./src/components/DashboardNew.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AudioVisualizerNew__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AudioVisualizerNew */ \"(app-pages-browser)/./src/components/AudioVisualizerNew.tsx\");\n/* harmony import */ var _QuickStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuickStats */ \"(app-pages-browser)/./src/components/QuickStats.tsx\");\n/* harmony import */ var _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioEngine */ \"(app-pages-browser)/./src/hooks/useAudioEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardNew() {\n    _s();\n    const { getAnalyserData, isInitialized, isPlaying, initializeAudio } = (0,_hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_4__.useAudioEngine)();\n    const [currentTrack, setCurrentTrack] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: 'Aucune musique détectée',\n        artist: 'En attente...',\n        genre: 'Inconnu',\n        confidence: 0\n    });\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const tracks = [\n        {\n            title: 'Bohemian Rhapsody',\n            artist: 'Queen',\n            genre: 'Rock',\n            confidence: 95\n        },\n        {\n            title: 'Billie Jean',\n            artist: 'Michael Jackson',\n            genre: 'Pop',\n            confidence: 92\n        },\n        {\n            title: 'Hotel California',\n            artist: 'Eagles',\n            genre: 'Rock',\n            confidence: 88\n        },\n        {\n            title: 'Imagine',\n            artist: 'John Lennon',\n            genre: 'Folk',\n            confidence: 90\n        }\n    ];\n    const updateTrack = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardNew.useCallback[updateTrack]\": ()=>{\n            if (isListening) {\n                const randomTrack = tracks[Math.floor(Math.random() * tracks.length)];\n                setCurrentTrack(randomTrack);\n            }\n        }\n    }[\"DashboardNew.useCallback[updateTrack]\"], [\n        isListening\n    ]);\n    // Simulation de détection audio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardNew.useEffect\": ()=>{\n            const interval = setInterval(updateTrack, 4000);\n            return ({\n                \"DashboardNew.useEffect\": ()=>clearInterval(interval)\n            })[\"DashboardNew.useEffect\"];\n        }\n    }[\"DashboardNew.useEffect\"], [\n        updateTrack\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Vue d'ensemble de votre exp\\xe9rience audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsListening(!isListening),\n                                className: \"px-6 py-3 rounded font-medium \".concat(isListening ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'),\n                                children: isListening ? '⏸️ Arrêter l\\'écoute' : '▶️ Démarrer l\\'écoute'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            !isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: initializeAudio,\n                                className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm\",\n                                children: \"Initialiser Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-blue-600 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83C\\uDFB5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: currentTrack.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: currentTrack.artist\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm\",\n                                            children: currentTrack.genre\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                \"Confiance: \",\n                                                currentTrack.confidence,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full \".concat(isListening ? 'bg-green-500' : 'bg-gray-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: isListening ? 'En écoute' : 'Arrêté'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Statut Audio Engine\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(isInitialized ? 'bg-green-500' : 'bg-red-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: \"Contexte Audio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: isInitialized ? 'Initialisé' : 'Non initialisé'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(isPlaying ? 'bg-blue-500' : 'bg-gray-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: \"Lecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: isPlaying ? 'En cours' : 'Arrêté'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(isListening ? 'bg-yellow-500' : 'bg-gray-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: \"D\\xe9tection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: isListening ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AudioVisualizerNew__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isActive: isListening && isInitialized,\n                getAnalyserData: getAnalyserData,\n                title: \"Visualisation Audio en Temps R\\xe9el\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Activit\\xe9 R\\xe9cente\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            {\n                                time: '14:32',\n                                action: 'Égaliseur ajusté automatiquement',\n                                track: 'Bohemian Rhapsody',\n                                type: 'eq'\n                            },\n                            {\n                                time: '14:28',\n                                action: 'Nouveau genre détecté: Rock',\n                                track: 'Hotel California',\n                                type: 'detection'\n                            },\n                            {\n                                time: '14:25',\n                                action: 'Préférences mises à jour',\n                                track: 'Billie Jean',\n                                type: 'settings'\n                            },\n                            {\n                                time: '14:20',\n                                action: 'Session d\\'écoute démarrée',\n                                track: '-',\n                                type: 'session'\n                            }\n                        ].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-700/50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(activity.type === 'eq' ? 'bg-blue-500' : activity.type === 'detection' ? 'bg-green-500' : activity.type === 'settings' ? 'bg-yellow-500' : 'bg-purple-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm\",\n                                                        children: activity.action\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: activity.track\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: activity.time\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Actions Rapides\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-4 bg-blue-600/10 border border-blue-600/20 rounded hover:bg-blue-600/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl mb-2 block\",\n                                            children: \"\\uD83C\\uDF9B️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"\\xc9galiseur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-4 bg-green-600/10 border border-green-600/20 rounded hover:bg-green-600/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl mb-2 block\",\n                                            children: \"\\uD83E\\uDDE0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"Analyse IA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-4 bg-purple-600/10 border border-purple-600/20 rounded hover:bg-purple-600/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl mb-2 block\",\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"Historique\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-4 bg-orange-600/10 border border-orange-600/20 rounded hover:bg-orange-600/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl mb-2 block\",\n                                            children: \"⚙️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"Param\\xe8tres\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\DashboardNew.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardNew, \"HMcqS25+DsomUW5DhyJhysnu0qc=\", false, function() {\n    return [\n        _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_4__.useAudioEngine\n    ];\n});\n_c = DashboardNew;\nvar _c;\n$RefreshReg$(_c, \"DashboardNew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0Rhc2hib2FyZE5ldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRXdEO0FBQ0g7QUFDaEI7QUFDa0I7QUFFeEMsU0FBU007O0lBQ3RCLE1BQU0sRUFBRUMsZUFBZSxFQUFFQyxhQUFhLEVBQUVDLFNBQVMsRUFBRUMsZUFBZSxFQUFFLEdBQUdMLHFFQUFjQTtJQUVyRixNQUFNLENBQUNNLGNBQWNDLGdCQUFnQixHQUFHWiwrQ0FBUUEsQ0FBQztRQUMvQ2EsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsWUFBWTtJQUNkO0lBRUEsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUUvQyxNQUFNbUIsU0FBUztRQUNiO1lBQUVOLE9BQU87WUFBcUJDLFFBQVE7WUFBU0MsT0FBTztZQUFRQyxZQUFZO1FBQUc7UUFDN0U7WUFBRUgsT0FBTztZQUFlQyxRQUFRO1lBQW1CQyxPQUFPO1lBQU9DLFlBQVk7UUFBRztRQUNoRjtZQUFFSCxPQUFPO1lBQW9CQyxRQUFRO1lBQVVDLE9BQU87WUFBUUMsWUFBWTtRQUFHO1FBQzdFO1lBQUVILE9BQU87WUFBV0MsUUFBUTtZQUFlQyxPQUFPO1lBQVFDLFlBQVk7UUFBRztLQUMxRTtJQUVELE1BQU1JLGNBQWNsQixrREFBV0E7aURBQUM7WUFDOUIsSUFBSWUsYUFBYTtnQkFDZixNQUFNSSxjQUFjRixNQUFNLENBQUNHLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLTCxPQUFPTSxNQUFNLEVBQUU7Z0JBQ3JFYixnQkFBZ0JTO1lBQ2xCO1FBQ0Y7Z0RBQUc7UUFBQ0o7S0FBWTtJQUVoQixnQ0FBZ0M7SUFDaENoQixnREFBU0E7a0NBQUM7WUFDUixNQUFNeUIsV0FBV0MsWUFBWVAsYUFBYTtZQUMxQzswQ0FBTyxJQUFNUSxjQUFjRjs7UUFDN0I7aUNBQUc7UUFBQ047S0FBWTtJQUVoQixxQkFDRSw4REFBQ1M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDRTtnQ0FBR0QsV0FBVTswQ0FBcUM7Ozs7OzswQ0FDbkQsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O2tDQUUvQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FDQ0MsU0FBUyxJQUFNaEIsZUFBZSxDQUFDRDtnQ0FDL0JhLFdBQVcsaUNBSVYsT0FIQ2IsY0FDSSwyQ0FDQTswQ0FHTEEsY0FBYyx5QkFBeUI7Ozs7Ozs0QkFHekMsQ0FBQ1QsK0JBQ0EsOERBQUN5QjtnQ0FDQ0MsU0FBU3hCO2dDQUNUb0IsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFQLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0s7Z0NBQUtMLFdBQVU7MENBQVc7Ozs7Ozs7Ozs7O3NDQUU3Qiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FBb0NuQixhQUFhRSxLQUFLOzs7Ozs7OENBQ3BFLDhEQUFDbUI7b0NBQUVGLFdBQVU7OENBQWlCbkIsYUFBYUcsTUFBTTs7Ozs7OzhDQUNqRCw4REFBQ2U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSzs0Q0FBS0wsV0FBVTtzREFDYm5CLGFBQWFJLEtBQUs7Ozs7OztzREFFckIsOERBQUNvQjs0Q0FBS0wsV0FBVTs7Z0RBQXdCO2dEQUMxQm5CLGFBQWFLLFVBQVU7Z0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSTFDLDhEQUFDYTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFXLHdCQUFxRSxPQUE3Q2IsY0FBYyxpQkFBaUI7Ozs7Ozs4Q0FDdkUsOERBQUNlO29DQUFFRixXQUFVOzhDQUNWYixjQUFjLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9yQyw4REFBQ1k7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVyx3QkFBc0UsT0FBOUN0QixnQkFBZ0IsaUJBQWlCOzs7Ozs7a0RBQ3pFLDhEQUFDcUI7OzBEQUNDLDhEQUFDRztnREFBRUYsV0FBVTswREFBeUI7Ozs7OzswREFDdEMsOERBQUNFO2dEQUFFRixXQUFVOzBEQUF5QnRCLGdCQUFnQixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSXpFLDhEQUFDcUI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVyx3QkFBa0UsT0FBMUNyQixZQUFZLGdCQUFnQjs7Ozs7O2tEQUNwRSw4REFBQ29COzswREFDQyw4REFBQ0c7Z0RBQUVGLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3RDLDhEQUFDRTtnREFBRUYsV0FBVTswREFBeUJyQixZQUFZLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJbkUsOERBQUNvQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFXLHdCQUFzRSxPQUE5Q2IsY0FBYyxrQkFBa0I7Ozs7OztrREFDeEUsOERBQUNZOzswREFDQyw4REFBQ0c7Z0RBQUVGLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3RDLDhEQUFDRTtnREFBRUYsV0FBVTswREFBeUJiLGNBQWMsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU92RSw4REFBQ2QsMkRBQWtCQTtnQkFDakJrQyxVQUFVcEIsZUFBZVQ7Z0JBQ3pCRCxpQkFBaUJBO2dCQUNqQk0sT0FBTTs7Ozs7OzBCQUlSLDhEQUFDVCxtREFBVUE7Ozs7OzBCQUdYLDhEQUFDeUI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaOzRCQUNDO2dDQUFFUSxNQUFNO2dDQUFTQyxRQUFRO2dDQUFvQ0MsT0FBTztnQ0FBcUJDLE1BQU07NEJBQUs7NEJBQ3BHO2dDQUFFSCxNQUFNO2dDQUFTQyxRQUFRO2dDQUErQkMsT0FBTztnQ0FBb0JDLE1BQU07NEJBQVk7NEJBQ3JHO2dDQUFFSCxNQUFNO2dDQUFTQyxRQUFRO2dDQUE0QkMsT0FBTztnQ0FBZUMsTUFBTTs0QkFBVzs0QkFDNUY7Z0NBQUVILE1BQU07Z0NBQVNDLFFBQVE7Z0NBQThCQyxPQUFPO2dDQUFLQyxNQUFNOzRCQUFVO3lCQUNwRixDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsVUFBVUMsc0JBQ2YsOERBQUNmO2dDQUFnQkMsV0FBVTs7a0RBQ3pCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFXLHdCQUtmLE9BSkNhLFNBQVNGLElBQUksS0FBSyxPQUFPLGdCQUN6QkUsU0FBU0YsSUFBSSxLQUFLLGNBQWMsaUJBQ2hDRSxTQUFTRixJQUFJLEtBQUssYUFBYSxrQkFDL0I7Ozs7OzswREFFRiw4REFBQ1o7O2tFQUNDLDhEQUFDRzt3REFBRUYsV0FBVTtrRUFBc0JhLFNBQVNKLE1BQU07Ozs7OztrRUFDbEQsOERBQUNQO3dEQUFFRixXQUFVO2tFQUF5QmEsU0FBU0gsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUd4RCw4REFBQ0w7d0NBQUtMLFdBQVU7a0RBQXlCYSxTQUFTTCxJQUFJOzs7Ozs7OytCQWI5Q007Ozs7Ozs7Ozs7Ozs7Ozs7MEJBb0JoQiw4REFBQ2Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU9ILFdBQVU7MENBQ2hCLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUFLTCxXQUFVO3NEQUFzQjs7Ozs7O3NEQUN0Qyw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQWlDOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJbEQsOERBQUNHO2dDQUFPSCxXQUFVOzBDQUNoQiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSzs0Q0FBS0wsV0FBVTtzREFBc0I7Ozs7OztzREFDdEMsOERBQUNFOzRDQUFFRixXQUFVO3NEQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSWxELDhEQUFDRztnQ0FBT0gsV0FBVTswQ0FDaEIsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0s7NENBQUtMLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQ3RDLDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUlsRCw4REFBQ0c7Z0NBQU9ILFdBQVU7MENBQ2hCLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUFLTCxXQUFVO3NEQUFzQjs7Ozs7O3NEQUN0Qyw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU81RDtHQWxNd0J4Qjs7UUFDaURELGlFQUFjQTs7O0tBRC9EQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBbGEgS2FsYm91c3NpXFxEZXNrdG9wXFxFdmVyeVxcZnBcXGVnYWxpc2V1clxcc3JjXFxjb21wb25lbnRzXFxEYXNoYm9hcmROZXcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IEF1ZGlvVmlzdWFsaXplck5ldyBmcm9tICcuL0F1ZGlvVmlzdWFsaXplck5ldydcbmltcG9ydCBRdWlja1N0YXRzIGZyb20gJy4vUXVpY2tTdGF0cydcbmltcG9ydCB7IHVzZUF1ZGlvRW5naW5lIH0gZnJvbSAnQC9ob29rcy91c2VBdWRpb0VuZ2luZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkTmV3KCkge1xuICBjb25zdCB7IGdldEFuYWx5c2VyRGF0YSwgaXNJbml0aWFsaXplZCwgaXNQbGF5aW5nLCBpbml0aWFsaXplQXVkaW8gfSA9IHVzZUF1ZGlvRW5naW5lKClcbiAgXG4gIGNvbnN0IFtjdXJyZW50VHJhY2ssIHNldEN1cnJlbnRUcmFja10gPSB1c2VTdGF0ZSh7XG4gICAgdGl0bGU6ICdBdWN1bmUgbXVzaXF1ZSBkw6l0ZWN0w6llJyxcbiAgICBhcnRpc3Q6ICdFbiBhdHRlbnRlLi4uJyxcbiAgICBnZW5yZTogJ0luY29ubnUnLFxuICAgIGNvbmZpZGVuY2U6IDBcbiAgfSlcblxuICBjb25zdCBbaXNMaXN0ZW5pbmcsIHNldElzTGlzdGVuaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgY29uc3QgdHJhY2tzID0gW1xuICAgIHsgdGl0bGU6ICdCb2hlbWlhbiBSaGFwc29keScsIGFydGlzdDogJ1F1ZWVuJywgZ2VucmU6ICdSb2NrJywgY29uZmlkZW5jZTogOTUgfSxcbiAgICB7IHRpdGxlOiAnQmlsbGllIEplYW4nLCBhcnRpc3Q6ICdNaWNoYWVsIEphY2tzb24nLCBnZW5yZTogJ1BvcCcsIGNvbmZpZGVuY2U6IDkyIH0sXG4gICAgeyB0aXRsZTogJ0hvdGVsIENhbGlmb3JuaWEnLCBhcnRpc3Q6ICdFYWdsZXMnLCBnZW5yZTogJ1JvY2snLCBjb25maWRlbmNlOiA4OCB9LFxuICAgIHsgdGl0bGU6ICdJbWFnaW5lJywgYXJ0aXN0OiAnSm9obiBMZW5ub24nLCBnZW5yZTogJ0ZvbGsnLCBjb25maWRlbmNlOiA5MCB9XG4gIF1cblxuICBjb25zdCB1cGRhdGVUcmFjayA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaXNMaXN0ZW5pbmcpIHtcbiAgICAgIGNvbnN0IHJhbmRvbVRyYWNrID0gdHJhY2tzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHRyYWNrcy5sZW5ndGgpXVxuICAgICAgc2V0Q3VycmVudFRyYWNrKHJhbmRvbVRyYWNrKVxuICAgIH1cbiAgfSwgW2lzTGlzdGVuaW5nXSlcblxuICAvLyBTaW11bGF0aW9uIGRlIGTDqXRlY3Rpb24gYXVkaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKHVwZGF0ZVRyYWNrLCA0MDAwKVxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKVxuICB9LCBbdXBkYXRlVHJhY2tdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+RGFzaGJvYXJkPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+VnVlIGQnZW5zZW1ibGUgZGUgdm90cmUgZXhww6lyaWVuY2UgYXVkaW88L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTGlzdGVuaW5nKCFpc0xpc3RlbmluZyl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTMgcm91bmRlZCBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICBpc0xpc3RlbmluZyBcbiAgICAgICAgICAgICAgICA/ICdiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAgdGV4dC13aGl0ZScgXG4gICAgICAgICAgICAgICAgOiAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzTGlzdGVuaW5nID8gJ+KPuO+4jyBBcnLDqnRlciBsXFwnw6ljb3V0ZScgOiAn4pa277iPIETDqW1hcnJlciBsXFwnw6ljb3V0ZSd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgeyFpc0luaXRpYWxpemVkICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aW5pdGlhbGl6ZUF1ZGlvfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgcm91bmRlZCB0ZXh0LXdoaXRlIHRleHQtc21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBJbml0aWFsaXNlciBBdWRpb1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEF1ZGlvIFN0YXR1cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWJsdWUtNjAwIHJvdW5kZWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+8J+OtTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e2N1cnJlbnRUcmFjay50aXRsZX08L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPntjdXJyZW50VHJhY2suYXJ0aXN0fTwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IG10LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWJsdWUtNjAwLzIwIHRleHQtYmx1ZS0zMDAgcm91bmRlZC1mdWxsIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7Y3VycmVudFRyYWNrLmdlbnJlfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIENvbmZpYW5jZToge2N1cnJlbnRUcmFjay5jb25maWRlbmNlfSVcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7aXNMaXN0ZW5pbmcgPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1ncmF5LTUwMCd9YH0+PC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMVwiPlxuICAgICAgICAgICAgICB7aXNMaXN0ZW5pbmcgPyAnRW4gw6ljb3V0ZScgOiAnQXJyw6p0w6knfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQXVkaW8gRW5naW5lIFN0YXR1cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPlN0YXR1dCBBdWRpbyBFbmdpbmU8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCAke2lzSW5pdGlhbGl6ZWQgPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1yZWQtNTAwJ31gfT48L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5Db250ZXh0ZSBBdWRpbzwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+e2lzSW5pdGlhbGl6ZWQgPyAnSW5pdGlhbGlzw6knIDogJ05vbiBpbml0aWFsaXPDqSd9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtpc1BsYXlpbmcgPyAnYmctYmx1ZS01MDAnIDogJ2JnLWdyYXktNTAwJ31gfT48L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5MZWN0dXJlPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj57aXNQbGF5aW5nID8gJ0VuIGNvdXJzJyA6ICdBcnLDqnTDqSd9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtpc0xpc3RlbmluZyA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1ncmF5LTUwMCd9YH0+PC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+RMOpdGVjdGlvbjwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+e2lzTGlzdGVuaW5nID8gJ0FjdGl2ZScgOiAnSW5hY3RpdmUnfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQXVkaW8gVmlzdWFsaXplciAqL31cbiAgICAgIDxBdWRpb1Zpc3VhbGl6ZXJOZXcgXG4gICAgICAgIGlzQWN0aXZlPXtpc0xpc3RlbmluZyAmJiBpc0luaXRpYWxpemVkfSBcbiAgICAgICAgZ2V0QW5hbHlzZXJEYXRhPXtnZXRBbmFseXNlckRhdGF9XG4gICAgICAgIHRpdGxlPVwiVmlzdWFsaXNhdGlvbiBBdWRpbyBlbiBUZW1wcyBSw6llbFwiXG4gICAgICAvPlxuXG4gICAgICB7LyogUXVpY2sgU3RhdHMgKi99XG4gICAgICA8UXVpY2tTdGF0cyAvPlxuXG4gICAgICB7LyogUmVjZW50IEFjdGl2aXR5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+QWN0aXZpdMOpIFLDqWNlbnRlPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICB7W1xuICAgICAgICAgICAgeyB0aW1lOiAnMTQ6MzInLCBhY3Rpb246ICfDiWdhbGlzZXVyIGFqdXN0w6kgYXV0b21hdGlxdWVtZW50JywgdHJhY2s6ICdCb2hlbWlhbiBSaGFwc29keScsIHR5cGU6ICdlcScgfSxcbiAgICAgICAgICAgIHsgdGltZTogJzE0OjI4JywgYWN0aW9uOiAnTm91dmVhdSBnZW5yZSBkw6l0ZWN0w6k6IFJvY2snLCB0cmFjazogJ0hvdGVsIENhbGlmb3JuaWEnLCB0eXBlOiAnZGV0ZWN0aW9uJyB9LFxuICAgICAgICAgICAgeyB0aW1lOiAnMTQ6MjUnLCBhY3Rpb246ICdQcsOpZsOpcmVuY2VzIG1pc2VzIMOgIGpvdXInLCB0cmFjazogJ0JpbGxpZSBKZWFuJywgdHlwZTogJ3NldHRpbmdzJyB9LFxuICAgICAgICAgICAgeyB0aW1lOiAnMTQ6MjAnLCBhY3Rpb246ICdTZXNzaW9uIGRcXCfDqWNvdXRlIGTDqW1hcnLDqWUnLCB0cmFjazogJy0nLCB0eXBlOiAnc2Vzc2lvbicgfVxuICAgICAgICAgIF0ubWFwKChhY3Rpdml0eSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmF5LTcwMC81MCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZpdHkudHlwZSA9PT0gJ2VxJyA/ICdiZy1ibHVlLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgYWN0aXZpdHkudHlwZSA9PT0gJ2RldGVjdGlvbicgPyAnYmctZ3JlZW4tNTAwJyA6XG4gICAgICAgICAgICAgICAgICBhY3Rpdml0eS50eXBlID09PSAnc2V0dGluZ3MnID8gJ2JnLXllbGxvdy01MDAnIDpcbiAgICAgICAgICAgICAgICAgICdiZy1wdXJwbGUtNTAwJ1xuICAgICAgICAgICAgICAgIH1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtXCI+e2FjdGl2aXR5LmFjdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj57YWN0aXZpdHkudHJhY2t9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+e2FjdGl2aXR5LnRpbWV9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBRdWljayBBY3Rpb25zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+QWN0aW9ucyBSYXBpZGVzPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTQgYmctYmx1ZS02MDAvMTAgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMCByb3VuZGVkIGhvdmVyOmJnLWJsdWUtNjAwLzIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTIgYmxvY2tcIj7wn46b77iPPC9zcGFuPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj7DiWdhbGlzZXVyPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTQgYmctZ3JlZW4tNjAwLzEwIGJvcmRlciBib3JkZXItZ3JlZW4tNjAwLzIwIHJvdW5kZWQgaG92ZXI6YmctZ3JlZW4tNjAwLzIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTIgYmxvY2tcIj7wn6egPC9zcGFuPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj5BbmFseXNlIElBPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTQgYmctcHVycGxlLTYwMC8xMCBib3JkZXIgYm9yZGVyLXB1cnBsZS02MDAvMjAgcm91bmRlZCBob3ZlcjpiZy1wdXJwbGUtNjAwLzIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTIgYmxvY2tcIj7wn5OKPC9zcGFuPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj5IaXN0b3JpcXVlPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTQgYmctb3JhbmdlLTYwMC8xMCBib3JkZXIgYm9yZGVyLW9yYW5nZS02MDAvMjAgcm91bmRlZCBob3ZlcjpiZy1vcmFuZ2UtNjAwLzIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTIgYmxvY2tcIj7impnvuI88L3NwYW4+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bVwiPlBhcmFtw6h0cmVzPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiQXVkaW9WaXN1YWxpemVyTmV3IiwiUXVpY2tTdGF0cyIsInVzZUF1ZGlvRW5naW5lIiwiRGFzaGJvYXJkTmV3IiwiZ2V0QW5hbHlzZXJEYXRhIiwiaXNJbml0aWFsaXplZCIsImlzUGxheWluZyIsImluaXRpYWxpemVBdWRpbyIsImN1cnJlbnRUcmFjayIsInNldEN1cnJlbnRUcmFjayIsInRpdGxlIiwiYXJ0aXN0IiwiZ2VucmUiLCJjb25maWRlbmNlIiwiaXNMaXN0ZW5pbmciLCJzZXRJc0xpc3RlbmluZyIsInRyYWNrcyIsInVwZGF0ZVRyYWNrIiwicmFuZG9tVHJhY2siLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJsZW5ndGgiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwiaDMiLCJpc0FjdGl2ZSIsInRpbWUiLCJhY3Rpb24iLCJ0cmFjayIsInR5cGUiLCJtYXAiLCJhY3Rpdml0eSIsImluZGV4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DashboardNew.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/EqualizerNew.tsx":
/*!*****************************************!*\
  !*** ./src/components/EqualizerNew.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EqualizerNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioEngine */ \"(app-pages-browser)/./src/hooks/useAudioEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst PRESETS = {\n    flat: {\n        name: 'Plat',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    },\n    rock: {\n        name: 'Rock',\n        values: {\n            '32Hz': 4,\n            '64Hz': 3,\n            '125Hz': 1,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 3,\n            '8kHz': 4,\n            '16kHz': 3\n        }\n    },\n    pop: {\n        name: 'Pop',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 0,\n            '250Hz': 1,\n            '500Hz': 2,\n            '1kHz': 2,\n            '2kHz': 1,\n            '4kHz': 0,\n            '8kHz': 1,\n            '16kHz': 1\n        }\n    },\n    classical: {\n        name: 'Classique',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': -1,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 2\n        }\n    },\n    jazz: {\n        name: 'Jazz',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 1,\n            '250Hz': 2,\n            '500Hz': 1,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 1\n        }\n    },\n    electronic: {\n        name: 'Électronique',\n        values: {\n            '32Hz': 5,\n            '64Hz': 4,\n            '125Hz': 2,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 2,\n            '8kHz': 1,\n            '16kHz': 0\n        }\n    },\n    vocal: {\n        name: 'Vocal',\n        values: {\n            '32Hz': -2,\n            '64Hz': -1,\n            '125Hz': 0,\n            '250Hz': 3,\n            '500Hz': 4,\n            '1kHz': 4,\n            '2kHz': 3,\n            '4kHz': 1,\n            '8kHz': 0,\n            '16kHz': -1\n        }\n    }\n};\nfunction EqualizerNew() {\n    _s();\n    const [selectedPreset, setSelectedPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('flat');\n    const [isTestPlaying, setIsTestPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isInitialized, isPlaying, volume, frequencies, error, initializeAudio, setVolume, setEQFrequency, resetEQ, playTestTone } = (0,_hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__.useAudioEngine)();\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleVolumeChange]\": (e)=>{\n            const newVolume = parseInt(e.target.value);\n            setVolume(newVolume);\n        }\n    }[\"EqualizerNew.useCallback[handleVolumeChange]\"], [\n        setVolume\n    ]);\n    const handleFrequencyChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleFrequencyChange]\": (freq, value)=>{\n            setEQFrequency(freq, value);\n            setSelectedPreset('custom');\n        }\n    }[\"EqualizerNew.useCallback[handleFrequencyChange]\"], [\n        setEQFrequency\n    ]);\n    const handlePresetChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handlePresetChange]\": (presetKey)=>{\n            const preset = PRESETS[presetKey];\n            if (!preset) return;\n            setSelectedPreset(presetKey);\n            Object.entries(preset.values).forEach({\n                \"EqualizerNew.useCallback[handlePresetChange]\": (param)=>{\n                    let [freq, gain] = param;\n                    setEQFrequency(freq, gain);\n                }\n            }[\"EqualizerNew.useCallback[handlePresetChange]\"]);\n        }\n    }[\"EqualizerNew.useCallback[handlePresetChange]\"], [\n        setEQFrequency\n    ]);\n    const handleTestTone = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleTestTone]\": async ()=>{\n            setIsTestPlaying(true);\n            await playTestTone(440);\n            setTimeout({\n                \"EqualizerNew.useCallback[handleTestTone]\": ()=>setIsTestPlaying(false)\n            }[\"EqualizerNew.useCallback[handleTestTone]\"], 2000);\n        }\n    }[\"EqualizerNew.useCallback[handleTestTone]\"], [\n        playTestTone\n    ]);\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleReset]\": ()=>{\n            resetEQ();\n            setSelectedPreset('flat');\n        }\n    }[\"EqualizerNew.useCallback[handleReset]\"], [\n        resetEQ\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"\\xc9galiseur Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Contr\\xf4lez et personnalisez votre exp\\xe9rience audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 rounded-full \".concat(isInitialized ? 'bg-green-500' : 'bg-yellow-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: isInitialized ? 'Audio prêt' : 'Non initialisé'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            !isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: initializeAudio,\n                                className: \"px-4 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm\",\n                                children: \"Initialiser Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm\",\n                        children: [\n                            \"❌ \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Test Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleTestTone,\n                                disabled: !isInitialized || isPlaying || isTestPlaying,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-white font-medium\",\n                                children: isPlaying || isTestPlaying ? '🔊 Son en cours...' : '🔊 Test Son (440Hz)'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Cliquez pour jouer un son de test et tester l'\\xe9galiseur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Volume Principal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83D\\uDD0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"100\",\n                                        value: volume,\n                                        onChange: handleVolumeChange,\n                                        disabled: !isInitialized,\n                                        className: \"flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer disabled:cursor-not-allowed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium w-12\",\n                                        children: [\n                                            volume,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Volume actuel : \",\n                                    volume,\n                                    \"% \",\n                                    volume === 0 ? '(Muet)' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Pr\\xe9r\\xe9glages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: [\n                            Object.entries(PRESETS).map((param)=>{\n                                let [key, preset] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePresetChange(key),\n                                    disabled: !isInitialized,\n                                    className: \"p-3 rounded text-left transition-colors disabled:opacity-50 disabled:cursor-not-allowed \".concat(selectedPreset === key ? 'bg-blue-600/20 border border-blue-600/40 text-blue-300' : 'bg-gray-700 hover:bg-gray-600 text-white'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: preset.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                disabled: !isInitialized,\n                                className: \"p-3 rounded text-left bg-gray-700 hover:bg-gray-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-sm\",\n                                    children: \"\\uD83D\\uDD04 Reset\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-6\",\n                        children: \"\\xc9galiseur 10 Bandes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 md:grid-cols-10 gap-4\",\n                        children: Object.entries(frequencies).map((param)=>{\n                            let [freq, value] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-medium\",\n                                        children: freq\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 w-8 bg-gray-700 rounded relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"-12\",\n                                                max: \"12\",\n                                                value: value,\n                                                onChange: (e)=>handleFrequencyChange(freq, parseInt(e.target.value)),\n                                                disabled: !isInitialized,\n                                                className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed\",\n                                                style: {\n                                                    writingMode: 'bt-lr',\n                                                    WebkitAppearance: 'slider-vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 w-full rounded transition-all \".concat(value >= 0 ? 'bg-blue-500' : 'bg-red-500'),\n                                                style: {\n                                                    height: \"\".concat(50 + value * 2, \"%\"),\n                                                    minHeight: '4px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute w-6 h-2 bg-white rounded shadow-lg\",\n                                                style: {\n                                                    bottom: \"\".concat(50 + value * 2 - 4, \"%\"),\n                                                    left: '50%',\n                                                    transform: 'translateX(-50%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white font-medium\",\n                                        children: [\n                                            value > 0 ? '+' : '',\n                                            value,\n                                            \"dB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, freq, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Instructions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Initialiser :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Cliquez sur \"Initialiser Audio\" si pas encore fait'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Test :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Utilisez \"Test Son\" pour g\\xe9n\\xe9rer un son de 440Hz'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Volume :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Ajustez le volume principal avec le slider\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Presets :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Cliquez sur un preset pour appliquer des r\\xe9glages pr\\xe9d\\xe9finis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\xc9galiseur :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Bougez les sliders pour ajuster chaque bande de fr\\xe9quence\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reset :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Remet tous les r\\xe9glages \\xe0 z\\xe9ro\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(EqualizerNew, \"eVsgi+bECZLK/v3HT11nOeNZyoQ=\", false, function() {\n    return [\n        _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__.useAudioEngine\n    ];\n});\n_c = EqualizerNew;\nvar _c;\n$RefreshReg$(_c, \"EqualizerNew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EqualizerNew.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/QuickStats.tsx":
/*!***************************************!*\
  !*** ./src/components/QuickStats.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuickStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction QuickStats() {\n    const stats = [\n        {\n            label: 'Morceaux analysés',\n            value: '1,247',\n            change: '+12%',\n            icon: '🎵',\n            color: 'text-blue-400'\n        },\n        {\n            label: 'Genres détectés',\n            value: '23',\n            change: '+3',\n            icon: '🎭',\n            color: 'text-green-400'\n        },\n        {\n            label: 'Précision IA',\n            value: '94.2%',\n            change: '+2.1%',\n            icon: '🧠',\n            color: 'text-purple-400'\n        },\n        {\n            label: 'Temps d\\'écoute',\n            value: '47h',\n            change: '+8h',\n            icon: '⏱️',\n            color: 'text-orange-400'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-white mt-1\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1 \".concat(stat.color),\n                                    children: [\n                                        stat.change,\n                                        \" cette semaine\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl\",\n                            children: stat.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\QuickStats.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = QuickStats;\nvar _c;\n$RefreshReg$(_c, \"QuickStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuickStats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAudioEngine.ts":
/*!*************************************!*\
  !*** ./src/hooks/useAudioEngine.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioEngine: () => (/* binding */ useAudioEngine)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioEngine auto */ \nclass AudioEngine {\n    async initialize() {\n        try {\n            console.log('🎵 Initializing AudioEngine...');\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n            this.gainNode = this.audioContext.createGain();\n            this.analyserNode = this.audioContext.createAnalyser();\n            this.eqNodes = this.eqFrequencies.map((freq, index)=>{\n                const filter = this.audioContext.createBiquadFilter();\n                if (index === 0) {\n                    filter.type = 'lowshelf';\n                } else if (index === this.eqFrequencies.length - 1) {\n                    filter.type = 'highshelf';\n                } else {\n                    filter.type = 'peaking';\n                }\n                filter.frequency.value = freq;\n                filter.Q.value = 1;\n                filter.gain.value = 0;\n                return filter;\n            });\n            this.connectAudioChain();\n            this.analyserNode.fftSize = 256;\n            this.analyserNode.smoothingTimeConstant = 0.8;\n            this.gainNode.gain.value = 0.75;\n            console.log('✅ AudioEngine initialized successfully');\n            return true;\n        } catch (error) {\n            console.error('❌ Failed to initialize AudioEngine:', error);\n            return false;\n        }\n    }\n    connectAudioChain() {\n        if (!this.gainNode || !this.analyserNode) return;\n        let currentNode = this.gainNode;\n        this.eqNodes.forEach((eqNode)=>{\n            currentNode.connect(eqNode);\n            currentNode = eqNode;\n        });\n        currentNode.connect(this.analyserNode);\n        this.analyserNode.connect(this.audioContext.destination);\n    }\n    async playTestTone() {\n        let frequency = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 440, duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2;\n        if (!this.audioContext || !this.gainNode) {\n            throw new Error('AudioEngine not initialized');\n        }\n        try {\n            this.stopTestTone();\n            this.oscillator = this.audioContext.createOscillator();\n            this.oscillator.type = 'sine';\n            this.oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);\n            this.oscillator.connect(this.gainNode);\n            this.oscillator.start();\n            this.oscillator.stop(this.audioContext.currentTime + duration);\n            console.log(\"\\uD83D\\uDD0A Playing test tone: \".concat(frequency, \"Hz for \").concat(duration, \"s\"));\n        } catch (error) {\n            console.error('❌ Error playing test tone:', error);\n            throw error;\n        }\n    }\n    stopTestTone() {\n        if (this.oscillator) {\n            try {\n                this.oscillator.stop();\n            } catch (e) {\n            // Ignore si déjà arrêté\n            }\n            this.oscillator = null;\n        }\n    }\n    setVolume(volume) {\n        if (!this.gainNode || !this.audioContext) return;\n        const clampedVolume = Math.max(0, Math.min(100, volume));\n        const gainValue = clampedVolume / 100;\n        this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);\n        console.log(\"\\uD83D\\uDD0A Volume set to: \".concat(clampedVolume, \"% (gain: \").concat(gainValue, \")\"));\n    }\n    setEQBand(bandIndex, gain) {\n        if (!this.eqNodes[bandIndex] || !this.audioContext) return;\n        const clampedGain = Math.max(-12, Math.min(12, gain));\n        this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime);\n        const freq = this.eqFrequencies[bandIndex];\n        console.log(\"\\uD83C\\uDF9B️ EQ \".concat(freq, \"Hz set to: \").concat(clampedGain, \"dB\"));\n    }\n    resetEQ() {\n        this.eqNodes.forEach((node, index)=>{\n            if (this.audioContext) {\n                node.gain.setValueAtTime(0, this.audioContext.currentTime);\n            }\n        });\n        console.log('🔄 EQ reset to flat');\n    }\n    getAnalyserData() {\n        if (!this.analyserNode) return new Uint8Array(0);\n        const bufferLength = this.analyserNode.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        this.analyserNode.getByteFrequencyData(dataArray);\n        return dataArray;\n    }\n    connectAudioElement(audioElement) {\n        if (!this.audioContext || !this.gainNode) return;\n        try {\n            if (this.sourceNode) {\n                this.sourceNode.disconnect();\n            }\n            this.sourceNode = this.audioContext.createMediaElementSource(audioElement);\n            this.sourceNode.connect(this.gainNode);\n            console.log('🎵 Audio element connected');\n        } catch (error) {\n            console.error('❌ Error connecting audio element:', error);\n        }\n    }\n    destroy() {\n        this.stopTestTone();\n        if (this.sourceNode) {\n            this.sourceNode.disconnect();\n        }\n        if (this.audioContext) {\n            this.audioContext.close();\n        }\n        console.log('🗑️ AudioEngine destroyed');\n    }\n    constructor(){\n        this.audioContext = null;\n        this.gainNode = null;\n        this.analyserNode = null;\n        this.eqNodes = [];\n        this.oscillator = null;\n        this.sourceNode = null;\n        this.eqFrequencies = [\n            32,\n            64,\n            125,\n            250,\n            500,\n            1000,\n            2000,\n            4000,\n            8000,\n            16000\n        ];\n    }\n}\nconst useAudioEngine = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isInitialized: false,\n        isPlaying: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        },\n        currentPreset: 'flat',\n        error: null\n    });\n    const audioEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const playTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const initializeAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[initializeAudio]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                if (!audioEngineRef.current) {\n                    audioEngineRef.current = new AudioEngine();\n                }\n                const success = await audioEngineRef.current.initialize();\n                setState({\n                    \"useAudioEngine.useCallback[initializeAudio]\": (prev)=>({\n                            ...prev,\n                            isInitialized: success,\n                            error: success ? null : 'Failed to initialize audio'\n                        })\n                }[\"useAudioEngine.useCallback[initializeAudio]\"]);\n            } catch (error) {\n                console.error('❌ Failed to initialize audio:', error);\n                setState({\n                    \"useAudioEngine.useCallback[initializeAudio]\": (prev)=>({\n                            ...prev,\n                            error: error instanceof Error ? error.message : 'Unknown error'\n                        })\n                }[\"useAudioEngine.useCallback[initializeAudio]\"]);\n            }\n        }\n    }[\"useAudioEngine.useCallback[initializeAudio]\"], [\n        state.isInitialized\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[setVolume]\": (volume)=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            audioEngineRef.current.setVolume(volume);\n            setState({\n                \"useAudioEngine.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume\n                    })\n            }[\"useAudioEngine.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioEngine.useCallback[setVolume]\"], [\n        state.isInitialized\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1) return;\n            audioEngineRef.current.setEQBand(freqIndex, gain);\n            setState({\n                \"useAudioEngine.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: gain\n                        }\n                    })\n            }[\"useAudioEngine.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioEngine.useCallback[setEQFrequency]\"], [\n        state.isInitialized,\n        state.frequencies\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[resetEQ]\": ()=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            audioEngineRef.current.resetEQ();\n            setState({\n                \"useAudioEngine.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioEngine.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioEngine.useCallback[resetEQ]\"], {}),\n                        currentPreset: 'flat'\n                    })\n            }[\"useAudioEngine.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioEngine.useCallback[resetEQ]\"], [\n        state.isInitialized\n    ]);\n    const playTestTone = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[playTestTone]\": async function() {\n            let frequency = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 440;\n            if (!audioEngineRef.current || !state.isInitialized) {\n                await initializeAudio();\n                if (!audioEngineRef.current) return;\n            }\n            try {\n                setState({\n                    \"useAudioEngine.useCallback[playTestTone]\": (prev)=>({\n                            ...prev,\n                            isPlaying: true,\n                            error: null\n                        })\n                }[\"useAudioEngine.useCallback[playTestTone]\"]);\n                await audioEngineRef.current.playTestTone(frequency, 2);\n                if (playTimeoutRef.current) {\n                    clearTimeout(playTimeoutRef.current);\n                }\n                playTimeoutRef.current = setTimeout({\n                    \"useAudioEngine.useCallback[playTestTone]\": ()=>{\n                        setState({\n                            \"useAudioEngine.useCallback[playTestTone]\": (prev)=>({\n                                    ...prev,\n                                    isPlaying: false\n                                })\n                        }[\"useAudioEngine.useCallback[playTestTone]\"]);\n                    }\n                }[\"useAudioEngine.useCallback[playTestTone]\"], 2000);\n            } catch (error) {\n                console.error('❌ Error playing test tone:', error);\n                setState({\n                    \"useAudioEngine.useCallback[playTestTone]\": (prev)=>({\n                            ...prev,\n                            isPlaying: false,\n                            error: error instanceof Error ? error.message : 'Failed to play test tone'\n                        })\n                }[\"useAudioEngine.useCallback[playTestTone]\"]);\n            }\n        }\n    }[\"useAudioEngine.useCallback[playTestTone]\"], [\n        state.isInitialized,\n        initializeAudio\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioEngine.useCallback[getAnalyserData]\": ()=>{\n            if (!audioEngineRef.current || !state.isInitialized) {\n                return new Uint8Array(0);\n            }\n            return audioEngineRef.current.getAnalyserData();\n        }\n    }[\"useAudioEngine.useCallback[getAnalyserData]\"], [\n        state.isInitialized\n    ]);\n    // Cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAudioEngine.useEffect\": ()=>{\n            return ({\n                \"useAudioEngine.useEffect\": ()=>{\n                    if (playTimeoutRef.current) {\n                        clearTimeout(playTimeoutRef.current);\n                    }\n                    if (audioEngineRef.current) {\n                        audioEngineRef.current.destroy();\n                    }\n                }\n            })[\"useAudioEngine.useEffect\"];\n        }\n    }[\"useAudioEngine.useEffect\"], []);\n    return {\n        ...state,\n        initializeAudio,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        playTestTone,\n        getAnalyserData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioEngine.ts\n"));

/***/ })

});