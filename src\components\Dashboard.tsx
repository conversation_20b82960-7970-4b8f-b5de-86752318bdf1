'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import AudioVisualizer from './AudioVisualizer'
import QuickStats from './QuickStats'

export default function Dashboard() {
  const [currentTrack, setCurrentTrack] = useState({
    title: 'Aucune musique détectée',
    artist: 'En attente...',
    genre: 'Inconnu',
    confidence: 0
  })

  const [isListening, setIsListening] = useState(true)

  const tracks = useMemo(() => [
    { title: 'Bohemian Rhapsody', artist: 'Queen', genre: 'Rock', confidence: 95 },
    { title: '<PERSON>', artist: '<PERSON>', genre: 'Pop', confidence: 92 },
    { title: 'Hotel California', artist: 'Eagles', genre: 'Rock', confidence: 88 },
    { title: 'Imagine', artist: '<PERSON>', genre: 'Folk', confidence: 90 }
  ], [])

  const updateTrack = useCallback(() => {
    if (isListening) {
      const randomTrack = tracks[Math.floor(Math.random() * tracks.length)]
      setCurrentTrack(randomTrack)
    }
  }, [isListening, tracks])

  // Simulation de détection audio
  useEffect(() => {
    const interval = setInterval(updateTrack, 4000) // Plus rapide
    return () => clearInterval(interval)
  }, [updateTrack])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-gray-400">Vue d'ensemble de votre expérience audio</p>
        </div>
        <button
          onClick={() => setIsListening(!isListening)}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isListening
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {isListening ? '⏸️ Arrêter l\'écoute' : '▶️ Démarrer l\'écoute'}
        </button>
      </div>

      {/* Current Track Info */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
            <span className="text-2xl">🎵</span>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-white">{currentTrack.title}</h3>
            <p className="text-gray-400">{currentTrack.artist}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="px-3 py-1 bg-primary-600/20 text-primary-300 rounded-full text-sm">
                {currentTrack.genre}
              </span>
              <span className="text-sm text-gray-400">
                Confiance: {currentTrack.confidence}%
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className={`w-3 h-3 rounded-full ${isListening ? 'bg-green-500' : 'bg-gray-500'}`}></div>
            <p className="text-xs text-gray-400 mt-1">
              {isListening ? 'En écoute' : 'Arrêté'}
            </p>
          </div>
        </div>
      </div>

      {/* Audio Visualizer */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Visualisation Audio</h3>
        <AudioVisualizer isActive={isListening} />
      </div>

      {/* Quick Stats */}
      <QuickStats />

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Activité Récente</h3>
        <div className="space-y-3">
          {[
            { time: '14:32', action: 'Égaliseur ajusté automatiquement', track: 'Bohemian Rhapsody' },
            { time: '14:28', action: 'Nouveau genre détecté: Rock', track: 'Hotel California' },
            { time: '14:25', action: 'Préférences mises à jour', track: 'Billie Jean' },
            { time: '14:20', action: 'Session d\'écoute démarrée', track: '-' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-dark-700/50 rounded-lg">
              <div>
                <p className="text-white text-sm">{activity.action}</p>
                <p className="text-gray-400 text-xs">{activity.track}</p>
              </div>
              <span className="text-gray-400 text-xs">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
