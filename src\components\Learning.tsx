'use client'

import { useState } from 'react'

export default function Learning() {
  const [feedbackMode, setFeedbackMode] = useState(true)
  const [currentSuggestion, setCurrentSuggestion] = useState({
    track: 'Bohemian Rhapsody',
    artist: 'Queen',
    suggestedGenre: 'Rock',
    suggestedSettings: {
      bass: 3,
      mid: 1,
      treble: 2
    },
    confidence: 95
  })

  const trainingData = [
    { genre: 'Rock', samples: 1247, accuracy: 94.2, lastTrained: '2 jours' },
    { genre: 'Pop', samples: 892, accuracy: 91.8, lastTrained: '3 jours' },
    { genre: 'Jazz', samples: 634, accuracy: 89.5, lastTrained: '1 semaine' },
    { genre: 'Classique', samples: 445, accuracy: 96.1, lastTrained: '4 jours' },
    { genre: 'Électronique', samples: 321, accuracy: 87.3, lastTrained: '5 jours' }
  ]

  const recentFeedback = [
    {
      id: 1,
      timestamp: '14:32',
      track: 'Hotel California',
      action: 'Correction genre: Rock → Folk Rock',
      impact: '+0.3% précision'
    },
    {
      id: 2,
      timestamp: '14:28',
      track: '<PERSON>',
      action: 'Ajustement basses: +2dB → +1dB',
      impact: 'Préférence enregistrée'
    },
    {
      id: 3,
      timestamp: '14:25',
      track: 'Imagine',
      action: 'Validation automatique',
      impact: '+0.1% confiance'
    }
  ]

  const handleFeedback = (type: 'correct' | 'incorrect' | 'adjust') => {
    // Simulation de feedback
    console.log(`Feedback: ${type} pour ${currentSuggestion.track}`)
    
    // Simuler un nouveau morceau
    const tracks = [
      { track: 'Stairway to Heaven', artist: 'Led Zeppelin', suggestedGenre: 'Rock' },
      { track: 'Thriller', artist: 'Michael Jackson', suggestedGenre: 'Pop' },
      { track: 'Take Five', artist: 'Dave Brubeck', suggestedGenre: 'Jazz' }
    ]
    
    const randomTrack = tracks[Math.floor(Math.random() * tracks.length)]
    setCurrentSuggestion({
      ...randomTrack,
      suggestedSettings: {
        bass: Math.floor(Math.random() * 6) - 3,
        mid: Math.floor(Math.random() * 6) - 3,
        treble: Math.floor(Math.random() * 6) - 3
      },
      confidence: 85 + Math.random() * 15
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Apprentissage IA</h1>
        <p className="text-gray-400">Entraînez et améliorez l'intelligence artificielle</p>
      </div>

      {/* Feedback Mode Toggle */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-white">Mode Feedback</h3>
            <p className="text-gray-400 text-sm">Activez pour corriger les suggestions de l'IA</p>
          </div>
          <button
            onClick={() => setFeedbackMode(!feedbackMode)}
            className={`relative w-12 h-6 rounded-full transition-colors ${
              feedbackMode ? 'bg-primary-600' : 'bg-gray-600'
            }`}
          >
            <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
              feedbackMode ? 'translate-x-6' : 'translate-x-0.5'
            }`} />
          </button>
        </div>
        
        {feedbackMode && (
          <div className="p-4 bg-primary-600/10 border border-primary-600/20 rounded-lg">
            <p className="text-primary-300 text-sm">
              🎓 Mode apprentissage activé. Vos corrections aideront l'IA à s'améliorer.
            </p>
          </div>
        )}
      </div>

      {/* Current Suggestion */}
      {feedbackMode && (
        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">Suggestion Actuelle</h3>
          <div className="bg-dark-700/50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-xl font-bold text-white">{currentSuggestion.track}</h4>
                <p className="text-gray-400">{currentSuggestion.artist}</p>
              </div>
              <div className="text-right">
                <span className="px-3 py-1 bg-primary-600/20 text-primary-300 rounded-full text-sm">
                  {currentSuggestion.suggestedGenre}
                </span>
                <p className="text-gray-400 text-sm mt-1">
                  Confiance: {Math.round(currentSuggestion.confidence)}%
                </p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <p className="text-gray-400 text-sm">Basses</p>
                <p className="text-white font-bold">
                  {currentSuggestion.suggestedSettings.bass > 0 ? '+' : ''}
                  {currentSuggestion.suggestedSettings.bass}dB
                </p>
              </div>
              <div className="text-center">
                <p className="text-gray-400 text-sm">Médiums</p>
                <p className="text-white font-bold">
                  {currentSuggestion.suggestedSettings.mid > 0 ? '+' : ''}
                  {currentSuggestion.suggestedSettings.mid}dB
                </p>
              </div>
              <div className="text-center">
                <p className="text-gray-400 text-sm">Aigus</p>
                <p className="text-white font-bold">
                  {currentSuggestion.suggestedSettings.treble > 0 ? '+' : ''}
                  {currentSuggestion.suggestedSettings.treble}dB
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => handleFeedback('correct')}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                ✅ Correct
              </button>
              <button
                onClick={() => handleFeedback('adjust')}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                🔧 Ajuster
              </button>
              <button
                onClick={() => handleFeedback('incorrect')}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                ❌ Incorrect
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Training Progress */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Progression de l'Entraînement</h3>
        <div className="space-y-4">
          {trainingData.map((data, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-dark-700/50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-600/20 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🎭</span>
                </div>
                <div>
                  <h4 className="font-medium text-white">{data.genre}</h4>
                  <p className="text-gray-400 text-sm">{data.samples} échantillons</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-bold">{data.accuracy}%</p>
                <p className="text-gray-400 text-sm">Il y a {data.lastTrained}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Feedback */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Feedback Récent</h3>
        <div className="space-y-3">
          {recentFeedback.map((feedback) => (
            <div key={feedback.id} className="flex items-center justify-between p-3 bg-dark-700/50 rounded-lg">
              <div>
                <p className="text-white text-sm">{feedback.action}</p>
                <p className="text-gray-400 text-xs">{feedback.track}</p>
              </div>
              <div className="text-right">
                <span className="text-green-400 text-xs">{feedback.impact}</span>
                <p className="text-gray-400 text-xs">{feedback.timestamp}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Training Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Actions d'Entraînement</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 bg-primary-600/10 border border-primary-600/20 rounded-lg hover:bg-primary-600/20 transition-colors">
            <div className="text-center">
              <span className="text-2xl mb-2 block">🔄</span>
              <h4 className="font-medium text-white">Réentraîner le Modèle</h4>
              <p className="text-gray-400 text-sm">Utiliser tous les feedbacks récents</p>
            </div>
          </button>
          
          <button className="p-4 bg-green-600/10 border border-green-600/20 rounded-lg hover:bg-green-600/20 transition-colors">
            <div className="text-center">
              <span className="text-2xl mb-2 block">📊</span>
              <h4 className="font-medium text-white">Analyser les Performances</h4>
              <p className="text-gray-400 text-sm">Voir les métriques détaillées</p>
            </div>
          </button>
          
          <button className="p-4 bg-blue-600/10 border border-blue-600/20 rounded-lg hover:bg-blue-600/20 transition-colors">
            <div className="text-center">
              <span className="text-2xl mb-2 block">💾</span>
              <h4 className="font-medium text-white">Sauvegarder le Modèle</h4>
              <p className="text-gray-400 text-sm">Créer un point de sauvegarde</p>
            </div>
          </button>
          
          <button className="p-4 bg-orange-600/10 border border-orange-600/20 rounded-lg hover:bg-orange-600/20 transition-colors">
            <div className="text-center">
              <span className="text-2xl mb-2 block">🔄</span>
              <h4 className="font-medium text-white">Réinitialiser</h4>
              <p className="text-gray-400 text-sm">Revenir aux paramètres d'usine</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
