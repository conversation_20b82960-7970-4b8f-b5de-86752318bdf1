'use client'

import { useState, useRef, useCallback, useEffect } from 'react'

interface AudioState {
  isInitialized: boolean
  isPlaying: boolean
  volume: number
  frequencies: { [key: string]: number }
  currentPreset: string
  error: string | null
}

class AudioEngine {
  private audioContext: AudioContext | null = null
  private gainNode: GainNode | null = null
  private analyserNode: AnalyserNode | null = null
  private eqNodes: BiquadFilterNode[] = []
  private oscillator: OscillatorNode | null = null
  private sourceNode: MediaElementAudioSourceNode | null = null
  
  private readonly eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]
  
  async initialize(): Promise<boolean> {
    try {
      console.log('🎵 Initializing AudioEngine...')
      
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      
      this.gainNode = this.audioContext.createGain()
      this.analyserNode = this.audioContext.createAnalyser()
      
      this.eqNodes = this.eqFrequencies.map((freq, index) => {
        const filter = this.audioContext!.createBiquadFilter()
        
        if (index === 0) {
          filter.type = 'lowshelf'
        } else if (index === this.eqFrequencies.length - 1) {
          filter.type = 'highshelf'
        } else {
          filter.type = 'peaking'
        }
        
        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0
        
        return filter
      })
      
      this.connectAudioChain()
      
      this.analyserNode.fftSize = 256
      this.analyserNode.smoothingTimeConstant = 0.8
      
      this.gainNode.gain.value = 0.75
      
      console.log('✅ AudioEngine initialized successfully')
      return true
      
    } catch (error) {
      console.error('❌ Failed to initialize AudioEngine:', error)
      return false
    }
  }
  
  private connectAudioChain() {
    if (!this.gainNode || !this.analyserNode) return
    
    let currentNode: AudioNode = this.gainNode
    
    this.eqNodes.forEach(eqNode => {
      currentNode.connect(eqNode)
      currentNode = eqNode
    })
    
    currentNode.connect(this.analyserNode)
    this.analyserNode.connect(this.audioContext!.destination)
  }
  
  async playTestTone(frequency: number = 440, duration: number = 2): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('AudioEngine not initialized')
    }
    
    try {
      this.stopTestTone()
      
      this.oscillator = this.audioContext.createOscillator()
      this.oscillator.type = 'sine'
      this.oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime)
      
      this.oscillator.connect(this.gainNode)
      
      this.oscillator.start()
      this.oscillator.stop(this.audioContext.currentTime + duration)
      
      console.log(`🔊 Playing test tone: ${frequency}Hz for ${duration}s`)
      
    } catch (error) {
      console.error('❌ Error playing test tone:', error)
      throw error
    }
  }
  
  stopTestTone() {
    if (this.oscillator) {
      try {
        this.oscillator.stop()
      } catch (e) {
        // Ignore si déjà arrêté
      }
      this.oscillator = null
    }
  }
  
  setVolume(volume: number) {
    if (!this.gainNode || !this.audioContext) return
    
    const clampedVolume = Math.max(0, Math.min(100, volume))
    const gainValue = clampedVolume / 100
    
    this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime)
    console.log(`🔊 Volume set to: ${clampedVolume}% (gain: ${gainValue})`)
  }
  
  setEQBand(bandIndex: number, gain: number) {
    if (!this.eqNodes[bandIndex] || !this.audioContext) return
    
    const clampedGain = Math.max(-12, Math.min(12, gain))
    this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime)
    
    const freq = this.eqFrequencies[bandIndex]
    console.log(`🎛️ EQ ${freq}Hz set to: ${clampedGain}dB`)
  }
  
  resetEQ() {
    this.eqNodes.forEach((node, index) => {
      if (this.audioContext) {
        node.gain.setValueAtTime(0, this.audioContext.currentTime)
      }
    })
    console.log('🔄 EQ reset to flat')
  }
  
  getAnalyserData(): Uint8Array {
    if (!this.analyserNode) return new Uint8Array(0)
    
    const bufferLength = this.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }
  
  connectAudioElement(audioElement: HTMLAudioElement) {
    if (!this.audioContext || !this.gainNode) return
    
    try {
      if (this.sourceNode) {
        this.sourceNode.disconnect()
      }
      
      this.sourceNode = this.audioContext.createMediaElementSource(audioElement)
      this.sourceNode.connect(this.gainNode)
      
      console.log('🎵 Audio element connected')
    } catch (error) {
      console.error('❌ Error connecting audio element:', error)
    }
  }
  
  destroy() {
    this.stopTestTone()
    
    if (this.sourceNode) {
      this.sourceNode.disconnect()
    }
    
    if (this.audioContext) {
      this.audioContext.close()
    }
    
    console.log('🗑️ AudioEngine destroyed')
  }
}

export const useAudioEngine = () => {
  const [state, setState] = useState<AudioState>({
    isInitialized: false,
    isPlaying: false,
    volume: 75,
    frequencies: {
      '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0,
      '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0
    },
    currentPreset: 'flat',
    error: null
  })
  
  const audioEngineRef = useRef<AudioEngine | null>(null)
  const playTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const initializeAudio = useCallback(async () => {
    if (state.isInitialized) return
    
    try {
      if (!audioEngineRef.current) {
        audioEngineRef.current = new AudioEngine()
      }
      
      const success = await audioEngineRef.current.initialize()
      
      setState(prev => ({
        ...prev,
        isInitialized: success,
        error: success ? null : 'Failed to initialize audio'
      }))
      
    } catch (error) {
      console.error('❌ Failed to initialize audio:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [state.isInitialized])
  
  const setVolume = useCallback((volume: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    audioEngineRef.current.setVolume(volume)
    setState(prev => ({ ...prev, volume }))
  }, [state.isInitialized])
  
  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1) return
    
    audioEngineRef.current.setEQBand(freqIndex, gain)
    setState(prev => ({
      ...prev,
      frequencies: { ...prev.frequencies, [freqKey]: gain }
    }))
  }, [state.isInitialized, state.frequencies])
  
  const resetEQ = useCallback(() => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    audioEngineRef.current.resetEQ()
    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number }),
      currentPreset: 'flat'
    }))
  }, [state.isInitialized])
  
  const playTestTone = useCallback(async (frequency: number = 440) => {
    if (!audioEngineRef.current || !state.isInitialized) {
      await initializeAudio()
      if (!audioEngineRef.current) return
    }
    
    try {
      setState(prev => ({ ...prev, isPlaying: true, error: null }))
      
      await audioEngineRef.current.playTestTone(frequency, 2)
      
      if (playTimeoutRef.current) {
        clearTimeout(playTimeoutRef.current)
      }
      
      playTimeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, isPlaying: false }))
      }, 2000)
      
    } catch (error) {
      console.error('❌ Error playing test tone:', error)
      setState(prev => ({
        ...prev,
        isPlaying: false,
        error: error instanceof Error ? error.message : 'Failed to play test tone'
      }))
    }
  }, [state.isInitialized, initializeAudio])
  
  const getAnalyserData = useCallback(() => {
    if (!audioEngineRef.current || !state.isInitialized) {
      return new Uint8Array(0)
    }
    
    return audioEngineRef.current.getAnalyserData()
  }, [state.isInitialized])
  
  // Cleanup
  useEffect(() => {
    return () => {
      if (playTimeoutRef.current) {
        clearTimeout(playTimeoutRef.current)
      }
      if (audioEngineRef.current) {
        audioEngineRef.current.destroy()
      }
    }
  }, [])

  return {
    ...state,
    initializeAudio,
    setVolume,
    setEQFrequency,
    resetEQ,
    playTestTone,
    getAnalyserData
  }
}
