'use client'

import { useState, useEffect } from 'react'

export default function AIAnalysis() {
  const [analysisData, setAnalysisData] = useState({
    genre: 'Rock',
    confidence: 94.2,
    bpm: 128,
    energy: 0.85,
    valence: 0.72,
    danceability: 0.68,
    acousticness: 0.15,
    instrumentalness: 0.02
  })

  const [spectrogramData, setSpectrogramData] = useState<number[][]>([])

  // Simulation de données de spectrogramme
  useEffect(() => {
    const generateSpectrogram = () => {
      const data = []
      for (let i = 0; i < 50; i++) {
        const row = []
        for (let j = 0; j < 20; j++) {
          row.push(Math.random() * 100)
        }
        data.push(row)
      }
      setSpectrogramData(data)
    }

    generateSpectrogram()
    const interval = setInterval(generateSpectrogram, 1000)
    return () => clearInterval(interval)
  }, [])

  const features = [
    { name: '<PERSON><PERSON><PERSON>', value: analysisData.energy, color: 'bg-red-500' },
    { name: 'Valence', value: analysisData.valence, color: 'bg-yellow-500' },
    { name: 'Dansabilité', value: analysisData.danceability, color: 'bg-green-500' },
    { name: 'Acoustique', value: analysisData.acousticness, color: 'bg-blue-500' },
    { name: 'Instrumental', value: analysisData.instrumentalness, color: 'bg-purple-500' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Analyse IA</h1>
        <p className="text-gray-400">Intelligence artificielle en temps réel</p>
      </div>

      {/* Current Analysis */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Analyse Actuelle</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center mb-3">
              <span className="text-2xl">🎭</span>
            </div>
            <h4 className="text-xl font-bold text-white">{analysisData.genre}</h4>
            <p className="text-gray-400">Genre détecté</p>
            <div className="mt-2">
              <span className="px-3 py-1 bg-green-600/20 text-green-300 rounded-full text-sm">
                {analysisData.confidence}% confiance
              </span>
            </div>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-500 to-green-700 rounded-full flex items-center justify-center mb-3">
              <span className="text-2xl">⚡</span>
            </div>
            <h4 className="text-xl font-bold text-white">{analysisData.bpm}</h4>
            <p className="text-gray-400">BPM</p>
            <div className="mt-2">
              <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                Tempo modéré
              </span>
            </div>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-orange-500 to-orange-700 rounded-full flex items-center justify-center mb-3">
              <span className="text-2xl">🎵</span>
            </div>
            <h4 className="text-xl font-bold text-white">{Math.round(analysisData.energy * 100)}%</h4>
            <p className="text-gray-400">Énergie</p>
            <div className="mt-2">
              <span className="px-3 py-1 bg-orange-600/20 text-orange-300 rounded-full text-sm">
                Énergique
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Audio Features */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Caractéristiques Audio</h3>
        <div className="space-y-4">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-24 text-sm text-gray-300">{feature.name}</div>
              <div className="flex-1 bg-dark-700 rounded-full h-3 relative">
                <div 
                  className={`${feature.color} h-full rounded-full transition-all duration-500`}
                  style={{ width: `${feature.value * 100}%` }}
                />
              </div>
              <div className="w-12 text-sm text-white text-right">
                {Math.round(feature.value * 100)}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Spectrogramme */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Spectrogramme en Temps Réel</h3>
        <div className="bg-dark-800 rounded-lg p-4 h-64 overflow-hidden">
          <div className="grid grid-cols-50 gap-px h-full">
            {spectrogramData.map((column, colIndex) => (
              <div key={colIndex} className="flex flex-col-reverse space-y-reverse space-y-px">
                {column.map((intensity, rowIndex) => (
                  <div
                    key={rowIndex}
                    className="w-full h-2"
                    style={{
                      backgroundColor: `hsl(${240 - intensity * 2.4}, 100%, ${20 + intensity * 0.6}%)`
                    }}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-2">
          <span>0 Hz</span>
          <span>Fréquence</span>
          <span>22 kHz</span>
        </div>
      </div>

      {/* AI Model Status */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Statut du Modèle IA</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-white mb-3">Performance</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Précision globale</span>
                <span className="text-green-400">94.2%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Temps de traitement</span>
                <span className="text-blue-400">12ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Échantillons traités</span>
                <span className="text-purple-400">1,247</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-3">Modèle</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Version</span>
                <span className="text-white">v2.1.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Dernière mise à jour</span>
                <span className="text-white">Il y a 2 jours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Statut</span>
                <span className="text-green-400 flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Actif
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
