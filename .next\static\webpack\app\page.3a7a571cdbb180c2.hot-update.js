"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst menuItems = [\n    {\n        id: 'dashboard',\n        label: 'Dashboard',\n        icon: '🏠',\n        description: 'Vue d\\'ensemble'\n    },\n    {\n        id: 'equalizer',\n        label: 'Égaliseur',\n        icon: '🎛️',\n        description: 'Contrôles audio'\n    },\n    {\n        id: 'ai-analysis',\n        label: 'Analyse IA',\n        icon: '🧠',\n        description: 'Intelligence artificielle'\n    },\n    {\n        id: 'history',\n        label: 'Historique',\n        icon: '📊',\n        description: 'Statistiques'\n    },\n    {\n        id: 'learning',\n        label: 'Apprentissage',\n        icon: '🎓',\n        description: 'Entraînement IA'\n    },\n    {\n        id: 'settings',\n        label: 'Paramètres',\n        icon: '⚙️',\n        description: 'Configuration'\n    }\n];\nfunction Sidebar(param) {\n    let { activeTab, setActiveTab } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(isCollapsed ? 'w-20' : 'w-64', \" transition-all duration-200 bg-dark-800/50 backdrop-blur-md border-r border-white/10\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold\",\n                                children: \"\\uD83C\\uDFB5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"WaveCraft\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Audio AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsCollapsed(!isCollapsed),\n                    className: \"mb-6 p-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-all duration-150\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: isCollapsed ? '→' : '←'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"space-y-2\",\n                    children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(item.id),\n                            className: \"nav-item w-full text-left \".concat(activeTab === item.id ? 'active' : ''),\n                            title: isCollapsed ? item.label : '',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: item.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-4 card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse-fast\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Statut\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"IA en \\xe9coute\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Audio d\\xe9tect\\xe9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"XL80Ke9pMdZ2JRKLtHkkSCCoQZ0=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});