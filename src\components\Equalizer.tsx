'use client'

import { useState, useCallback } from 'react'
import { useAudioContext } from '@/hooks/useAudioContext'
import AudioPlayer from './AudioPlayer'

export default function Equalizer() {
  const [isAutoMode, setIsAutoMode] = useState(false) // Commencer en mode manuel
  const [selectedPreset, setSelectedPreset] = useState('manual')

  const {
    frequencies,
    volume,
    isInitialized,
    connectAudioElement,
    setVolume,
    setEQFrequency,
    resetEQ
  } = useAudioContext()

  const presets = [
    { id: 'manual', name: 'Manuel', description: 'Réglages manuels' },
    { id: 'flat', name: 'Plat', description: 'Aucun ajustement' },
    { id: 'rock', name: 'Rock', description: 'Basses et aigus renforcés' },
    { id: 'pop', name: 'Pop', description: 'Équilibré pour la pop' },
    { id: 'classical', name: '<PERSON>ique', description: 'Naturel et équilibré' },
    { id: 'jazz', name: '<PERSON>', description: 'Médiums chauds' },
    { id: 'electronic', name: 'Électronique', description: 'Basses profondes' },
    { id: 'vocal', name: 'Vocal', description: 'Optimisé pour les voix' }
  ]

  const handleFrequencyChange = useCallback((freq: string, value: number) => {
    if (!isAutoMode) {
      setEQFrequency(freq, value)
    }
  }, [isAutoMode, setEQFrequency])

  const handlePresetChange = useCallback((presetId: string) => {
    setSelectedPreset(presetId)
    setIsAutoMode(false) // Toujours en mode manuel pour l'instant

    // Appliquer les presets
    const presetValues: { [key: string]: { [freq: string]: number } } = {
      flat: {
        '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0,
        '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0
      },
      rock: {
        '32Hz': 3, '64Hz': 2, '125Hz': 1, '250Hz': 0, '500Hz': -1,
        '1kHz': 0, '2kHz': 1, '4kHz': 2, '8kHz': 3, '16kHz': 2
      },
      pop: {
        '32Hz': 1, '64Hz': 1, '125Hz': 0, '250Hz': 1, '500Hz': 2,
        '1kHz': 2, '2kHz': 1, '4kHz': 0, '8kHz': 1, '16kHz': 1
      },
      classical: {
        '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0,
        '1kHz': 0, '2kHz': -1, '4kHz': -1, '8kHz': 0, '16kHz': 1
      },
      jazz: {
        '32Hz': 1, '64Hz': 0, '125Hz': 1, '250Hz': 2, '500Hz': 1,
        '1kHz': 0, '2kHz': 0, '4kHz': -1, '8kHz': 0, '16kHz': 1
      },
      electronic: {
        '32Hz': 4, '64Hz': 3, '125Hz': 2, '250Hz': 0, '500Hz': -1,
        '1kHz': 0, '2kHz': 1, '4kHz': 2, '8kHz': 1, '16kHz': 0
      },
      vocal: {
        '32Hz': -2, '64Hz': -1, '125Hz': 0, '250Hz': 2, '500Hz': 3,
        '1kHz': 3, '2kHz': 2, '4kHz': 1, '8kHz': 0, '16kHz': -1
      }
    }

    const preset = presetValues[presetId]
    if (preset) {
      Object.entries(preset).forEach(([freq, value]) => {
        setEQFrequency(freq, value)
      })
    }
  }, [setEQFrequency])

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume)
  }, [setVolume])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Égaliseur Audio</h1>
        <p className="text-gray-400">Contrôlez et personnalisez votre expérience audio</p>
        {!isInitialized && (
          <div className="mt-2 p-2 bg-yellow-600/20 border border-yellow-600/40 rounded text-yellow-300 text-sm">
            ⚠️ Contexte audio en cours d'initialisation...
          </div>
        )}
      </div>

      {/* Audio Player */}
      <AudioPlayer onAudioElementReady={connectAudioElement} />

      {/* Mode Toggle */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Mode de fonctionnement</h3>
          <div className="flex items-center space-x-3">
            <span className={`text-sm ${!isAutoMode ? 'text-white' : 'text-gray-400'}`}>Manuel</span>
            <button
              onClick={() => setIsAutoMode(!isAutoMode)}
              className={`relative w-12 h-6 rounded-full transition-colors ${
                isAutoMode ? 'bg-primary-600' : 'bg-gray-600'
              }`}
            >
              <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                isAutoMode ? 'translate-x-6' : 'translate-x-0.5'
              }`} />
            </button>
            <span className={`text-sm ${isAutoMode ? 'text-white' : 'text-gray-400'}`}>Auto IA</span>
          </div>
        </div>

        {isAutoMode && (
          <div className="p-4 bg-blue-600/10 border border-blue-600/20 rounded">
            <div className="flex items-center space-x-2">
              <span className="text-blue-400">🧠</span>
              <p className="text-blue-300 text-sm">
                L'IA ajuste automatiquement l'égaliseur en fonction du contenu audio détecté
              </p>
            </div>
          </div>
        )}

        <div className="flex space-x-2">
          <button
            onClick={resetEQ}
            className="btn-secondary"
            disabled={isAutoMode}
          >
            🔄 Reset
          </button>
        </div>
      </div>

      {/* Presets */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Préréglages</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {presets.map((preset) => (
            <button
              key={preset.id}
              onClick={() => handlePresetChange(preset.id)}
              className={`p-3 rounded-lg text-left transition-colors ${
                selectedPreset === preset.id
                  ? 'bg-primary-600/20 border border-primary-600/40'
                  : 'bg-dark-700/50 hover:bg-dark-600/50'
              }`}
            >
              <div className="font-medium text-white text-sm">{preset.name}</div>
              <div className="text-xs text-gray-400 mt-1">{preset.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Equalizer Sliders */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-6">Fréquences</h3>
        <div className="grid grid-cols-5 md:grid-cols-10 gap-4">
          {Object.entries(frequencies).map(([freq, value]) => (
            <div key={freq} className="flex flex-col items-center space-y-2">
              <div className="text-xs text-gray-400 font-medium">{freq}</div>
              <div className="h-32 w-8 bg-dark-700 rounded-full relative">
                <input
                  type="range"
                  min="-10"
                  max="10"
                  value={value}
                  onChange={(e) => handleFrequencyChange(freq, parseInt(e.target.value))}
                  disabled={isAutoMode}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                />
                <div
                  className={`absolute bottom-0 w-full rounded ${
                    value >= 0 ? 'bg-blue-500' : 'bg-red-500'
                  }`}
                  style={{
                    height: `${50 + (value * 2.5)}%`,
                    minHeight: '4px'
                  }}
                />
                <div
                  className="absolute w-6 h-2 bg-white rounded"
                  style={{
                    bottom: `${50 + (value * 2.5) - 4}%`,
                    left: '50%',
                    transform: 'translateX(-50%)'
                  }}
                />
              </div>
              <div className="text-xs text-white font-medium">
                {value > 0 ? '+' : ''}{value}dB
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Volume and Effects */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">Volume Principal</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <span className="text-2xl">🔊</span>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                className="flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer"
              />
              <span className="text-white font-medium w-12">{volume}%</span>
            </div>
            <div className="text-xs text-gray-400">
              Volume principal : {volume}% {volume === 0 ? '(Muet)' : ''}
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">Effets Audio</h3>
          <div className="space-y-3">
            {[
              { name: 'Bass Boost', enabled: true },
              { name: 'Virtualizer', enabled: false },
              { name: 'Reverb', enabled: false }
            ].map((effect, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-white">{effect.name}</span>
                <button
                  className={`w-10 h-6 rounded-full transition-colors ${
                    effect.enabled ? 'bg-primary-600' : 'bg-gray-600'
                  }`}
                >
                  <div className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    effect.enabled ? 'translate-x-5' : 'translate-x-1'
                  }`} />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
