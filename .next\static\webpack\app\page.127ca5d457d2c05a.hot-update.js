"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nclass AudioEngine {\n    async initialize() {\n        try {\n            console.log('🎵 Initializing AudioEngine...');\n            // Créer le contexte audio\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            // Reprendre le contexte si suspendu\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n            // Créer les nœuds audio\n            this.gainNode = this.audioContext.createGain();\n            this.analyserNode = this.audioContext.createAnalyser();\n            // Créer les filtres d'égaliseur\n            this.eqNodes = this.eqFrequencies.map((freq, index)=>{\n                const filter = this.audioContext.createBiquadFilter();\n                if (index === 0) {\n                    filter.type = 'lowshelf';\n                } else if (index === this.eqFrequencies.length - 1) {\n                    filter.type = 'highshelf';\n                } else {\n                    filter.type = 'peaking';\n                }\n                filter.frequency.value = freq;\n                filter.Q.value = 1;\n                filter.gain.value = 0;\n                return filter;\n            });\n            // Connecter la chaîne audio\n            this.connectAudioChain();\n            // Configurer l'analyseur\n            this.analyserNode.fftSize = 256;\n            this.analyserNode.smoothingTimeConstant = 0.8;\n            // Volume initial\n            this.gainNode.gain.value = 0.75;\n            console.log('✅ AudioEngine initialized successfully');\n            return true;\n        } catch (error) {\n            console.error('❌ Failed to initialize AudioEngine:', error);\n            return false;\n        }\n    }\n    connectAudioChain() {\n        if (!this.gainNode || !this.analyserNode) return;\n        // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination\n        let currentNode = this.gainNode;\n        this.eqNodes.forEach((eqNode)=>{\n            currentNode.connect(eqNode);\n            currentNode = eqNode;\n        });\n        currentNode.connect(this.analyserNode);\n        this.analyserNode.connect(this.audioContext.destination);\n    }\n    async playTestTone() {\n        let frequency = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 440, duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2;\n        if (!this.audioContext || !this.gainNode) {\n            throw new Error('AudioEngine not initialized');\n        }\n        try {\n            // Arrêter l'oscillateur précédent\n            this.stopTestTone();\n            // Créer un nouvel oscillateur\n            this.oscillator = this.audioContext.createOscillator();\n            this.oscillator.type = 'sine';\n            this.oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);\n            // Connecter à la chaîne audio\n            this.oscillator.connect(this.gainNode);\n            // Démarrer et programmer l'arrêt\n            this.oscillator.start();\n            this.oscillator.stop(this.audioContext.currentTime + duration);\n            console.log(\"\\uD83D\\uDD0A Playing test tone: \".concat(frequency, \"Hz for \").concat(duration, \"s\"));\n        } catch (error) {\n            console.error('❌ Error playing test tone:', error);\n            throw error;\n        }\n    }\n    stopTestTone() {\n        if (this.oscillator) {\n            try {\n                this.oscillator.stop();\n            } catch (e) {\n            // Ignore si déjà arrêté\n            }\n            this.oscillator = null;\n        }\n    }\n    setVolume(volume) {\n        if (!this.gainNode || !this.audioContext) return;\n        const clampedVolume = Math.max(0, Math.min(100, volume));\n        const gainValue = clampedVolume / 100;\n        this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);\n        console.log(\"\\uD83D\\uDD0A Volume set to: \".concat(clampedVolume, \"% (gain: \").concat(gainValue, \")\"));\n    }\n    setEQBand(bandIndex, gain) {\n        if (!this.eqNodes[bandIndex] || !this.audioContext) return;\n        const clampedGain = Math.max(-12, Math.min(12, gain));\n        this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime);\n        const freq = this.eqFrequencies[bandIndex];\n        console.log(\"\\uD83C\\uDF9B️ EQ \".concat(freq, \"Hz set to: \").concat(clampedGain, \"dB\"));\n    }\n    resetEQ() {\n        this.eqNodes.forEach((node, index)=>{\n            if (this.audioContext) {\n                node.gain.setValueAtTime(0, this.audioContext.currentTime);\n            }\n        });\n        console.log('🔄 EQ reset to flat');\n    }\n    getAnalyserData() {\n        if (!this.analyserNode) return new Uint8Array(0);\n        const bufferLength = this.analyserNode.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        this.analyserNode.getByteFrequencyData(dataArray);\n        return dataArray;\n    }\n    connectAudioElement(audioElement) {\n        if (!this.audioContext || !this.gainNode) return;\n        try {\n            // Déconnecter l'ancien source\n            if (this.sourceNode) {\n                this.sourceNode.disconnect();\n            }\n            // Créer et connecter le nouveau source\n            this.sourceNode = this.audioContext.createMediaElementSource(audioElement);\n            this.sourceNode.connect(this.gainNode);\n            console.log('🎵 Audio element connected');\n        } catch (error) {\n            console.error('❌ Error connecting audio element:', error);\n        }\n    }\n    destroy() {\n        this.stopTestTone();\n        if (this.sourceNode) {\n            this.sourceNode.disconnect();\n        }\n        if (this.audioContext) {\n            this.audioContext.close();\n        }\n        console.log('🗑️ AudioEngine destroyed');\n    }\n    constructor(){\n        this.audioContext = null;\n        this.gainNode = null;\n        this.analyserNode = null;\n        this.eqNodes = [];\n        this.oscillator = null;\n        this.sourceNode = null;\n        this.eqFrequencies = [\n            32,\n            64,\n            125,\n            250,\n            500,\n            1000,\n            2000,\n            4000,\n            8000,\n            16000\n        ];\n    }\n}\nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isInitialized: false,\n        isPlaying: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        },\n        currentPreset: 'flat',\n        error: null\n    });\n    const audioEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const playTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                console.log('Initializing audio context...');\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Reprendre le contexte si suspendu\n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully', {\n                    state: audioContext.state,\n                    sampleRate: audioContext.sampleRate\n                });\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], [\n        state.isInitialized\n    ]);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            console.log('Setting volume to:', volume);\n            if (!state.gainNode || !state.audioContext) {\n                console.log('No gain node or audio context available');\n                return;\n            }\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            try {\n                state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n                console.log('Volume set successfully:', gainValue);\n            } catch (error) {\n                console.error('Error setting volume:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            console.log('Setting EQ frequency:', freqKey, 'to gain:', gain);\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex] || !state.audioContext) {\n                console.log('EQ node not found or audio context not available');\n                return;\n            }\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            try {\n                eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n                console.log('EQ frequency set successfully:', freqKey, clampedGain);\n            } catch (error) {\n                console.error('Error setting EQ frequency:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    const playTestTone = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[playTestTone]\": async ()=>{\n            console.log('Playing test tone...');\n            if (!state.audioContext || !state.gainNode) {\n                console.log('Initializing audio context first...');\n                await initializeAudioContext();\n                return;\n            }\n            try {\n                // Arrêter l'oscillateur précédent s'il existe\n                if (oscillatorRef.current) {\n                    oscillatorRef.current.stop();\n                    oscillatorRef.current = null;\n                }\n                // Créer un nouvel oscillateur\n                const oscillator = state.audioContext.createOscillator();\n                oscillator.type = 'sine';\n                oscillator.frequency.setValueAtTime(440, state.audioContext.currentTime) // La note A4\n                ;\n                // Connecter l'oscillateur à la chaîne audio\n                oscillator.connect(state.gainNode);\n                // Démarrer et arrêter après 2 secondes\n                oscillator.start();\n                oscillator.stop(state.audioContext.currentTime + 2);\n                oscillatorRef.current = oscillator;\n                console.log('Test tone playing for 2 seconds');\n            } catch (error) {\n                console.error('Error playing test tone:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[playTestTone]\"], [\n        state.audioContext,\n        state.gainNode,\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData,\n        playTestTone\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});