'use client'

import { useState } from 'react'

export default function Settings() {
  const [settings, setSettings] = useState({
    audioDevice: 'default',
    sampleRate: '44100',
    bufferSize: '512',
    autoStart: true,
    notifications: true,
    darkMode: true,
    language: 'fr',
    aiSensitivity: 75,
    autoAdjust: true,
    saveHistory: true
  })

  const audioDevices = [
    { id: 'default', name: 'Périphérique par défaut' },
    { id: 'speakers', name: '<PERSON>ut-parle<PERSON> (Realtek)' },
    { id: 'headphones', name: 'Casque (USB)' },
    { id: 'bluetooth', name: 'Écouteurs Bluetooth' }
  ]

  const sampleRates = ['22050', '44100', '48000', '96000']
  const bufferSizes = ['128', '256', '512', '1024', '2048']
  const languages = [
    { code: 'fr', name: 'Français' },
    { code: 'en', name: 'English' },
    { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { code: 'de', name: '<PERSON><PERSON><PERSON>' }
  ]

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const handleSave = () => {
    // Simulation de sauvegarde
    console.log('Paramètres sauvegardés:', settings)
    alert('Paramètres sauvegardés avec succès!')
  }

  const handleReset = () => {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres?')) {
      setSettings({
        audioDevice: 'default',
        sampleRate: '44100',
        bufferSize: '512',
        autoStart: true,
        notifications: true,
        darkMode: true,
        language: 'fr',
        aiSensitivity: 75,
        autoAdjust: true,
        saveHistory: true
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Paramètres</h1>
        <p className="text-gray-400">Configurez WaveCraft selon vos préférences</p>
      </div>

      {/* Audio Settings */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Configuration Audio</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Périphérique Audio
            </label>
            <select
              value={settings.audioDevice}
              onChange={(e) => handleSettingChange('audioDevice', e.target.value)}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              {audioDevices.map((device) => (
                <option key={device.id} value={device.id}>
                  {device.name}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Fréquence d'échantillonnage
              </label>
              <select
                value={settings.sampleRate}
                onChange={(e) => handleSettingChange('sampleRate', e.target.value)}
                className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
              >
                {sampleRates.map((rate) => (
                  <option key={rate} value={rate}>
                    {rate} Hz
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Taille du buffer
              </label>
              <select
                value={settings.bufferSize}
                onChange={(e) => handleSettingChange('bufferSize', e.target.value)}
                className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
              >
                {bufferSizes.map((size) => (
                  <option key={size} value={size}>
                    {size} échantillons
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* AI Settings */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Intelligence Artificielle</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Sensibilité de l'IA ({settings.aiSensitivity}%)
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={settings.aiSensitivity}
              onChange={(e) => handleSettingChange('aiSensitivity', parseInt(e.target.value))}
              className="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>Moins sensible</span>
              <span>Plus sensible</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Ajustement automatique</h4>
              <p className="text-gray-400 text-sm">L'IA ajuste automatiquement l'égaliseur</p>
            </div>
            <button
              onClick={() => handleSettingChange('autoAdjust', !settings.autoAdjust)}
              className={`relative w-12 h-6 rounded-full transition-colors ${
                settings.autoAdjust ? 'bg-primary-600' : 'bg-gray-600'
              }`}
            >
              <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                settings.autoAdjust ? 'translate-x-6' : 'translate-x-0.5'
              }`} />
            </button>
          </div>
        </div>
      </div>

      {/* Application Settings */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Application</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Langue
            </label>
            <select
              value={settings.language}
              onChange={(e) => handleSettingChange('language', e.target.value)}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white">Démarrage automatique</h4>
                <p className="text-gray-400 text-sm">Démarrer WaveCraft au lancement du système</p>
              </div>
              <button
                onClick={() => handleSettingChange('autoStart', !settings.autoStart)}
                className={`relative w-12 h-6 rounded-full transition-colors ${
                  settings.autoStart ? 'bg-primary-600' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                  settings.autoStart ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white">Notifications</h4>
                <p className="text-gray-400 text-sm">Afficher les notifications système</p>
              </div>
              <button
                onClick={() => handleSettingChange('notifications', !settings.notifications)}
                className={`relative w-12 h-6 rounded-full transition-colors ${
                  settings.notifications ? 'bg-primary-600' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                  settings.notifications ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white">Mode sombre</h4>
                <p className="text-gray-400 text-sm">Interface en mode sombre</p>
              </div>
              <button
                onClick={() => handleSettingChange('darkMode', !settings.darkMode)}
                className={`relative w-12 h-6 rounded-full transition-colors ${
                  settings.darkMode ? 'bg-primary-600' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                  settings.darkMode ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white">Sauvegarder l'historique</h4>
                <p className="text-gray-400 text-sm">Conserver l'historique d'écoute</p>
              </div>
              <button
                onClick={() => handleSettingChange('saveHistory', !settings.saveHistory)}
                className={`relative w-12 h-6 rounded-full transition-colors ${
                  settings.saveHistory ? 'bg-primary-600' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${
                  settings.saveHistory ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Actions</h3>
        <div className="flex space-x-4">
          <button
            onClick={handleSave}
            className="btn-primary"
          >
            💾 Sauvegarder
          </button>
          <button
            onClick={handleReset}
            className="btn-secondary"
          >
            🔄 Réinitialiser
          </button>
          <button className="btn-secondary">
            📤 Exporter la configuration
          </button>
          <button className="btn-secondary">
            📥 Importer la configuration
          </button>
        </div>
      </div>

      {/* About */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">À propos</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-400">Version</span>
            <span className="text-white">1.0.0</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Dernière mise à jour</span>
            <span className="text-white">15 janvier 2024</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Modèle IA</span>
            <span className="text-white">WaveCraft AI v2.1.0</span>
          </div>
        </div>
      </div>
    </div>
  )
}
