"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_AIAnalysis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AIAnalysis */ \"(app-pages-browser)/./src/components/AIAnalysis.tsx\");\n/* harmony import */ var _components_History__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/History */ \"(app-pages-browser)/./src/components/History.tsx\");\n/* harmony import */ var _components_Settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _components_Learning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Learning */ \"(app-pages-browser)/./src/components/Learning.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n        }\n    }[\"Home.useCallback[handleTabChange]\"], []);\n    const renderContent = ()=>{\n        switch(activeTab){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 16\n                }, this);\n            case 'equalizer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Equalizer, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 16\n                }, this);\n            case 'ai-analysis':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAnalysis__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 16\n                }, this);\n            case 'history':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_History__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Settings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, this);\n            case 'learning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Learning__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                activeTab: activeTab,\n                setActiveTab: handleTabChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: renderContent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"nYYhcld3/ncl/1UkEalOJyXGeRY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});