# WaveCraft - Égaliseur Audio Intelligent

WaveCraft est une application d'égaliseur audio intelligent qui utilise l'intelligence artificielle pour analyser et optimiser automatiquement votre expérience d'écoute en temps réel.

## 🎯 Fonctionnalités

### Interface Utilisateur
- **Dashboard** - Vue d'ensemble avec visualisation audio en temps réel
- **Égaliseur** - Contrôles audio manuels et automatiques
- **Analyse IA** - Affichage de l'analyse en temps réel et classification musicale
- **Historique** - Statistiques et historique des morceaux analysés
- **Apprentissage** - Interface pour corriger et entraîner l'IA
- **Paramètres** - Configuration de l'application et préférences

### Fonctionnalités Principales
- 🧠 **Analyse audio en temps réel** par IA
- 🎛️ **Égaliseur automatique** basé sur le genre musical détecté
- 📊 **Visualisation spectrale** en temps réel
- 📈 **Historique et statistiques** d'écoute
- 🎓 **Apprentissage adaptatif** basé sur les préférences utilisateur
- 🌐 **Interface responsive** (desktop et mobile)
- 📱 **PWA** - Installation possible sur mobile

## 🚀 Technologies Utilisées

- **Frontend**: Next.js 15 + React 19
- **Styling**: Tailwind CSS
- **TypeScript**: Pour un code type-safe
- **PWA**: Progressive Web App pour mobile

## 📦 Installation

1. Clonez le repository
```bash
git clone [url-du-repo]
cd wavecraft
```

2. Installez les dépendances
```bash
npm install
```

3. Lancez le serveur de développement
```bash
npm run dev
```

4. Ouvrez [http://localhost:3000](http://localhost:3000) dans votre navigateur

## 🏗️ Structure du Projet

```
src/
├── app/
│   ├── globals.css          # Styles globaux
│   ├── layout.tsx           # Layout principal
│   └── page.tsx             # Page d'accueil
├── components/
│   ├── Sidebar.tsx          # Navigation latérale
│   ├── Dashboard.tsx        # Page dashboard
│   ├── Equalizer.tsx        # Interface égaliseur
│   ├── AIAnalysis.tsx       # Analyse IA
│   ├── History.tsx          # Historique
│   ├── Learning.tsx         # Apprentissage IA
│   ├── Settings.tsx         # Paramètres
│   ├── AudioVisualizer.tsx  # Visualiseur audio
│   └── QuickStats.tsx       # Statistiques rapides
public/
├── manifest.json            # Configuration PWA
└── [icons]                  # Icônes de l'application
```

## 🎨 Design

L'interface utilise un thème sombre moderne avec :
- Couleurs principales : Bleu (#0ea5e9) et gris foncé
- Design glassmorphism pour les cartes
- Animations fluides et transitions
- Interface responsive pour desktop et mobile

## 📱 Utilisation Mobile

L'application est conçue comme une PWA (Progressive Web App) :
1. Ouvrez l'application dans Chrome mobile
2. Appuyez sur "Ajouter à l'écran d'accueil"
3. L'application s'installera comme une app native

## 🔮 Fonctionnalités Futures

- Intégration avec l'API Web Audio pour capture audio réelle
- Modèle IA entraîné pour classification musicale
- Support des drivers audio virtuels
- Synchronisation cloud des préférences
- Support multi-langues complet

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commit vos changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence ISC.

## 🎵 Note

Cette version contient uniquement les interfaces utilisateur. Les fonctionnalités d'analyse audio et d'IA seront implémentées dans les prochaines versions.
