'use client'

import { useState, useCallback, useRef, useEffect } from 'react'

interface ScreenAudioState {
  isCapturing: boolean
  isInitialized: boolean
  volume: number
  frequencies: { [key: string]: number }
  error: string | null
  audioLevel: number
}

export default function ScreenAudioCapture() {
  const [state, setState] = useState<ScreenAudioState>({
    isCapturing: false,
    isInitialized: false,
    volume: 100,
    frequencies: {
      '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0,
      '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0
    },
    error: null,
    audioLevel: 0
  })

  const audioContextRef = useRef<AudioContext | null>(null)
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null)
  const gainNodeRef = useRef<GainNode | null>(null)
  const analyserNodeRef = useRef<AnalyserNode | null>(null)
  const eqNodesRef = useRef<BiquadFilterNode[]>([])
  const streamRef = useRef<MediaStream | null>(null)
  const outputAudioRef = useRef<HTMLAudioElement>(null)
  const levelIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]

  const initializeAudio = useCallback(async () => {
    try {
      console.log('🎵 Initializing Screen Audio Capture...')
      
      // Créer le contexte audio
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume()
      }
      
      // Créer les nœuds
      gainNodeRef.current = audioContextRef.current.createGain()
      analyserNodeRef.current = audioContextRef.current.createAnalyser()
      
      // Créer les filtres d'égaliseur
      eqNodesRef.current = eqFrequencies.map((freq, index) => {
        const filter = audioContextRef.current!.createBiquadFilter()
        
        if (index === 0) {
          filter.type = 'lowshelf'
        } else if (index === eqFrequencies.length - 1) {
          filter.type = 'highshelf'
        } else {
          filter.type = 'peaking'
        }
        
        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0
        
        return filter
      })
      
      // Configurer l'analyseur
      analyserNodeRef.current.fftSize = 256
      analyserNodeRef.current.smoothingTimeConstant = 0.8
      
      // Volume initial
      gainNodeRef.current.gain.value = 1.0
      
      setState(prev => ({ ...prev, isInitialized: true, error: null }))
      console.log('✅ Screen Audio Capture initialized')
      
    } catch (error) {
      console.error('❌ Failed to initialize screen audio:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Initialization failed'
      }))
    }
  }, [])

  const connectAudioChain = useCallback(() => {
    if (!sourceNodeRef.current || !gainNodeRef.current || !analyserNodeRef.current) return
    
    // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination
    let currentNode: AudioNode = sourceNodeRef.current
    
    // Volume
    currentNode.connect(gainNodeRef.current)
    currentNode = gainNodeRef.current
    
    // Égaliseur
    eqNodesRef.current.forEach(eqNode => {
      currentNode.connect(eqNode)
      currentNode = eqNode
    })
    
    // Analyseur et sortie
    currentNode.connect(analyserNodeRef.current)
    currentNode.connect(audioContextRef.current!.destination)
    
    console.log('🔗 Screen audio chain connected')
  }, [])

  const startScreenCapture = useCallback(async () => {
    if (!state.isInitialized) {
      await initializeAudio()
    }
    
    try {
      console.log('🖥️ Starting screen audio capture...')
      
      // Demander la capture d'écran avec audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: false,
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 44100
        } as any
      })
      
      streamRef.current = stream
      
      // Créer le source node
      sourceNodeRef.current = audioContextRef.current!.createMediaStreamSource(stream)
      
      // Connecter la chaîne audio
      connectAudioChain()
      
      // Démarrer le monitoring du niveau
      levelIntervalRef.current = setInterval(() => {
        if (analyserNodeRef.current) {
          const bufferLength = analyserNodeRef.current.frequencyBinCount
          const dataArray = new Uint8Array(bufferLength)
          analyserNodeRef.current.getByteFrequencyData(dataArray)
          
          let sum = 0
          for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i]
          }
          
          const level = (sum / bufferLength) / 255 * 100
          setState(prev => ({ ...prev, audioLevel: level }))
        }
      }, 100)
      
      setState(prev => ({ ...prev, isCapturing: true, error: null }))
      console.log('✅ Screen audio capture started')
      
    } catch (error) {
      console.error('❌ Error starting screen capture:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to start screen capture'
      }))
    }
  }, [state.isInitialized, initializeAudio, connectAudioChain])

  const stopCapture = useCallback(() => {
    console.log('⏹️ Stopping screen audio capture...')
    
    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect()
      sourceNodeRef.current = null
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    
    if (levelIntervalRef.current) {
      clearInterval(levelIntervalRef.current)
      levelIntervalRef.current = null
    }
    
    setState(prev => ({ 
      ...prev, 
      isCapturing: false,
      audioLevel: 0
    }))
    
    console.log('✅ Screen audio capture stopped')
  }, [])

  const setVolume = useCallback((volume: number) => {
    if (!gainNodeRef.current || !audioContextRef.current) return
    
    const clampedVolume = Math.max(0, Math.min(200, volume))
    const gainValue = clampedVolume / 100
    
    gainNodeRef.current.gain.setValueAtTime(gainValue, audioContextRef.current.currentTime)
    setState(prev => ({ ...prev, volume: clampedVolume }))
    
    console.log(`🔊 Screen volume set to: ${clampedVolume}%`)
  }, [])

  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    if (!audioContextRef.current) return
    
    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1 || !eqNodesRef.current[freqIndex]) return
    
    const clampedGain = Math.max(-12, Math.min(12, gain))
    eqNodesRef.current[freqIndex].gain.setValueAtTime(clampedGain, audioContextRef.current.currentTime)
    
    setState(prev => ({
      ...prev,
      frequencies: { ...prev.frequencies, [freqKey]: clampedGain }
    }))
    
    console.log(`🎛️ Screen EQ ${freqKey} set to: ${clampedGain}dB`)
  }, [state.frequencies])

  const resetEQ = useCallback(() => {
    if (!audioContextRef.current) return
    
    eqNodesRef.current.forEach((node) => {
      node.gain.setValueAtTime(0, audioContextRef.current!.currentTime)
    })
    
    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number })
    }))
    
    console.log('🔄 Screen EQ reset')
  }, [])

  // Cleanup
  useEffect(() => {
    return () => {
      stopCapture()
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [stopCapture])

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-white mb-4">Capture Audio Système (Expérimental)</h3>
      
      {/* Status */}
      <div className="flex items-center space-x-4 mb-4">
        <div className={`flex items-center space-x-2 px-3 py-1 rounded ${
          state.isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'
        }`}>
          <div className={`w-2 h-2 rounded-full ${state.isInitialized ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
          <span className="text-sm">
            {state.isInitialized ? 'Prêt' : 'Non initialisé'}
          </span>
        </div>
        
        <div className={`flex items-center space-x-2 px-3 py-1 rounded ${
          state.isCapturing ? 'bg-blue-600/20 text-blue-300' : 'bg-gray-600/20 text-gray-300'
        }`}>
          <div className={`w-2 h-2 rounded-full ${state.isCapturing ? 'bg-blue-500' : 'bg-gray-500'}`}></div>
          <span className="text-sm">
            {state.isCapturing ? 'Capture active' : 'Arrêté'}
          </span>
        </div>
      </div>

      {state.error && (
        <div className="mb-4 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm">
          ❌ {state.error}
        </div>
      )}

      {/* Controls */}
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={state.isCapturing ? stopCapture : startScreenCapture}
            className={`px-6 py-3 rounded font-medium ${
              state.isCapturing 
                ? 'bg-red-600 hover:bg-red-700 text-white' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {state.isCapturing ? '⏹️ Arrêter' : '🖥️ Capturer Audio Système'}
          </button>
          
          {!state.isInitialized && (
            <button
              onClick={initializeAudio}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white"
            >
              Initialiser
            </button>
          )}
        </div>

        {/* Audio Level */}
        {state.isCapturing && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Niveau audio:</span>
            <div className="flex-1 bg-gray-700 rounded-full h-2">
              <div 
                className={`h-full rounded-full transition-all duration-100 ${
                  state.audioLevel > 80 ? 'bg-red-500' : 
                  state.audioLevel > 50 ? 'bg-yellow-500' : 
                  'bg-green-500'
                }`}
                style={{ width: `${state.audioLevel}%` }}
              />
            </div>
            <span className="text-sm text-white w-12">{Math.round(state.audioLevel)}%</span>
          </div>
        )}

        {/* Volume Control */}
        {state.isCapturing && (
          <div className="space-y-2">
            <label className="text-sm text-gray-300">Volume de sortie</label>
            <div className="flex items-center space-x-4">
              <span className="text-xl">🔊</span>
              <input
                type="range"
                min="0"
                max="200"
                value={state.volume}
                onChange={(e) => setVolume(parseInt(e.target.value))}
                className="flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer"
              />
              <span className="text-white font-medium w-16">{state.volume}%</span>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-gray-700/50 rounded">
        <p className="text-sm text-gray-300 mb-2">
          <strong>⚠️ Fonctionnalité expérimentale</strong>
        </p>
        <ul className="text-xs text-gray-400 space-y-1">
          <li>• Cliquez sur "Capturer Audio Système" pour commencer</li>
          <li>• Sélectionnez "Partager l'audio système" dans la popup</li>
          <li>• L'audio de votre système sera traité par l'égaliseur</li>
          <li>• Fonctionne avec Chrome/Edge (pas Firefox)</li>
        </ul>
      </div>
    </div>
  )
}
