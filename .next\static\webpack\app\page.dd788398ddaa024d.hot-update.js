"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Equalizer.tsx":
/*!**************************************!*\
  !*** ./src/components/Equalizer.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Equalizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioContext */ \"(app-pages-browser)/./src/hooks/useAudioContext.ts\");\n/* harmony import */ var _AudioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AudioPlayer */ \"(app-pages-browser)/./src/components/AudioPlayer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Equalizer() {\n    _s();\n    const [isAutoMode, setIsAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // Commencer en mode manuel\n    ;\n    const [selectedPreset, setSelectedPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('manual');\n    const { frequencies, volume, isInitialized, connectAudioElement, setVolume, setEQFrequency, resetEQ } = (0,_hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__.useAudioContext)();\n    const presets = [\n        {\n            id: 'manual',\n            name: 'Manuel',\n            description: 'Réglages manuels'\n        },\n        {\n            id: 'flat',\n            name: 'Plat',\n            description: 'Aucun ajustement'\n        },\n        {\n            id: 'rock',\n            name: 'Rock',\n            description: 'Basses et aigus renforcés'\n        },\n        {\n            id: 'pop',\n            name: 'Pop',\n            description: 'Équilibré pour la pop'\n        },\n        {\n            id: 'classical',\n            name: 'Classique',\n            description: 'Naturel et équilibré'\n        },\n        {\n            id: 'jazz',\n            name: 'Jazz',\n            description: 'Médiums chauds'\n        },\n        {\n            id: 'electronic',\n            name: 'Électronique',\n            description: 'Basses profondes'\n        },\n        {\n            id: 'vocal',\n            name: 'Vocal',\n            description: 'Optimisé pour les voix'\n        }\n    ];\n    const handleFrequencyChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Equalizer.useCallback[handleFrequencyChange]\": (freq, value)=>{\n            if (!isAutoMode) {\n                setEQFrequency(freq, value);\n            }\n        }\n    }[\"Equalizer.useCallback[handleFrequencyChange]\"], [\n        isAutoMode,\n        setEQFrequency\n    ]);\n    const handlePresetChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Equalizer.useCallback[handlePresetChange]\": (presetId)=>{\n            setSelectedPreset(presetId);\n            setIsAutoMode(false) // Toujours en mode manuel pour l'instant\n            ;\n            // Appliquer les presets\n            const presetValues = {\n                flat: {\n                    '32Hz': 0,\n                    '64Hz': 0,\n                    '125Hz': 0,\n                    '250Hz': 0,\n                    '500Hz': 0,\n                    '1kHz': 0,\n                    '2kHz': 0,\n                    '4kHz': 0,\n                    '8kHz': 0,\n                    '16kHz': 0\n                },\n                rock: {\n                    '32Hz': 3,\n                    '64Hz': 2,\n                    '125Hz': 1,\n                    '250Hz': 0,\n                    '500Hz': -1,\n                    '1kHz': 0,\n                    '2kHz': 1,\n                    '4kHz': 2,\n                    '8kHz': 3,\n                    '16kHz': 2\n                },\n                pop: {\n                    '32Hz': 1,\n                    '64Hz': 1,\n                    '125Hz': 0,\n                    '250Hz': 1,\n                    '500Hz': 2,\n                    '1kHz': 2,\n                    '2kHz': 1,\n                    '4kHz': 0,\n                    '8kHz': 1,\n                    '16kHz': 1\n                },\n                classical: {\n                    '32Hz': 0,\n                    '64Hz': 0,\n                    '125Hz': 0,\n                    '250Hz': 0,\n                    '500Hz': 0,\n                    '1kHz': 0,\n                    '2kHz': -1,\n                    '4kHz': -1,\n                    '8kHz': 0,\n                    '16kHz': 1\n                },\n                jazz: {\n                    '32Hz': 1,\n                    '64Hz': 0,\n                    '125Hz': 1,\n                    '250Hz': 2,\n                    '500Hz': 1,\n                    '1kHz': 0,\n                    '2kHz': 0,\n                    '4kHz': -1,\n                    '8kHz': 0,\n                    '16kHz': 1\n                },\n                electronic: {\n                    '32Hz': 4,\n                    '64Hz': 3,\n                    '125Hz': 2,\n                    '250Hz': 0,\n                    '500Hz': -1,\n                    '1kHz': 0,\n                    '2kHz': 1,\n                    '4kHz': 2,\n                    '8kHz': 1,\n                    '16kHz': 0\n                },\n                vocal: {\n                    '32Hz': -2,\n                    '64Hz': -1,\n                    '125Hz': 0,\n                    '250Hz': 2,\n                    '500Hz': 3,\n                    '1kHz': 3,\n                    '2kHz': 2,\n                    '4kHz': 1,\n                    '8kHz': 0,\n                    '16kHz': -1\n                }\n            };\n            const preset = presetValues[presetId];\n            if (preset) {\n                Object.entries(preset).forEach({\n                    \"Equalizer.useCallback[handlePresetChange]\": (param)=>{\n                        let [freq, value] = param;\n                        setEQFrequency(freq, value);\n                    }\n                }[\"Equalizer.useCallback[handlePresetChange]\"]);\n            }\n        }\n    }[\"Equalizer.useCallback[handlePresetChange]\"], [\n        setEQFrequency\n    ]);\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Equalizer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            setVolume(newVolume);\n        }\n    }[\"Equalizer.useCallback[handleVolumeChange]\"], [\n        setVolume\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"\\xc9galiseur Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Contr\\xf4lez et personnalisez votre exp\\xe9rience audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    !isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 p-2 bg-yellow-600/20 border border-yellow-600/40 rounded text-yellow-300 text-sm\",\n                        children: \"⚠️ Contexte audio en cours d'initialisation...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onAudioElementReady: connectAudioElement\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: \"Mode de fonctionnement\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm \".concat(!isAutoMode ? 'text-white' : 'text-gray-400'),\n                                        children: \"Manuel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsAutoMode(!isAutoMode),\n                                        className: \"relative w-12 h-6 rounded-full transition-colors \".concat(isAutoMode ? 'bg-primary-600' : 'bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform \".concat(isAutoMode ? 'translate-x-6' : 'translate-x-0.5')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm \".concat(isAutoMode ? 'text-white' : 'text-gray-400'),\n                                        children: \"Auto IA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    isAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-600/10 border border-blue-600/20 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-400\",\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-300 text-sm\",\n                                    children: \"L'IA ajuste automatiquement l'\\xe9galiseur en fonction du contenu audio d\\xe9tect\\xe9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetEQ,\n                            className: \"btn-secondary\",\n                            disabled: isAutoMode,\n                            children: \"\\uD83D\\uDD04 Reset\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Pr\\xe9r\\xe9glages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: presets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePresetChange(preset.id),\n                                className: \"p-3 rounded-lg text-left transition-colors \".concat(selectedPreset === preset.id ? 'bg-primary-600/20 border border-primary-600/40' : 'bg-dark-700/50 hover:bg-dark-600/50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: preset.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: preset.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, preset.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-6\",\n                        children: \"Fr\\xe9quences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 md:grid-cols-10 gap-4\",\n                        children: Object.entries(frequencies).map((param)=>{\n                            let [freq, value] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-medium\",\n                                        children: freq\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 w-8 bg-dark-700 rounded-full relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"-10\",\n                                                max: \"10\",\n                                                value: value,\n                                                onChange: (e)=>handleFrequencyChange(freq, parseInt(e.target.value)),\n                                                disabled: isAutoMode,\n                                                className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 w-full rounded \".concat(value >= 0 ? 'bg-blue-500' : 'bg-red-500'),\n                                                style: {\n                                                    height: \"\".concat(50 + value * 2.5, \"%\"),\n                                                    minHeight: '4px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute w-6 h-2 bg-white rounded\",\n                                                style: {\n                                                    bottom: \"\".concat(50 + value * 2.5 - 4, \"%\"),\n                                                    left: '50%',\n                                                    transform: 'translateX(-50%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white font-medium\",\n                                        children: [\n                                            value > 0 ? '+' : '',\n                                            value,\n                                            \"dB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, freq, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Volume Principal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDD0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0\",\n                                            max: \"100\",\n                                            defaultValue: \"75\",\n                                            className: \"flex-1 h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-medium w-12\",\n                                            children: \"75%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Effets Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    {\n                                        name: 'Bass Boost',\n                                        enabled: true\n                                    },\n                                    {\n                                        name: 'Virtualizer',\n                                        enabled: false\n                                    },\n                                    {\n                                        name: 'Reverb',\n                                        enabled: false\n                                    }\n                                ].map((effect, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: effect.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-6 rounded-full transition-colors \".concat(effect.enabled ? 'bg-primary-600' : 'bg-gray-600'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-white rounded-full transition-transform \".concat(effect.enabled ? 'translate-x-5' : 'translate-x-1')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(Equalizer, \"mcgSQrzLfUwJ4JonqgeBYIefnvQ=\", false, function() {\n    return [\n        _hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__.useAudioContext\n    ];\n});\n_c = Equalizer;\nvar _c;\n$RefreshReg$(_c, \"Equalizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Equalizer.tsx\n"));

/***/ })

});