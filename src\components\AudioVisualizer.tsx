'use client'

import { useEffect, useState } from 'react'

interface AudioVisualizerProps {
  isActive: boolean
}

export default function AudioVisualizer({ isActive }: AudioVisualizerProps) {
  const [bars, setBars] = useState<number[]>(new Array(20).fill(0))

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isActive) {
      interval = setInterval(() => {
        setBars(prev => prev.map(() => Math.random() * 100))
      }, 100)
    } else {
      setBars(new Array(20).fill(0))
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isActive])

  return (
    <div className="h-32 flex items-end justify-center space-x-1 bg-dark-800/50 rounded-lg p-4">
      {bars.map((height, index) => (
        <div
          key={index}
          className="w-3 bg-gradient-to-t from-primary-600 to-primary-400 rounded-t transition-all duration-100"
          style={{
            height: `${Math.max(height, 5)}%`,
            animationDelay: `${index * 0.05}s`
          }}
        />
      ))}
    </div>
  )
}
