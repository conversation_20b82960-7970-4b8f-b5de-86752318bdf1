'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'

interface AudioVisualizerProps {
  isActive: boolean
}

export default function AudioVisualizer({ isActive }: AudioVisualizerProps) {
  const [bars, setBars] = useState<number[]>(new Array(15).fill(0))

  const updateBars = useCallback(() => {
    setBars(prev => prev.map(() => Math.random() * 100))
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isActive) {
      interval = setInterval(updateBars, 80) // Plus rapide
    } else {
      setBars(new Array(15).fill(0))
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isActive, updateBars])

  const barElements = useMemo(() =>
    bars.map((height, index) => (
      <div
        key={index}
        className="w-4 bg-gradient-to-t from-primary-600 to-primary-400 rounded-t transition-all duration-75"
        style={{
          height: `${Math.max(height, 8)}%`,
          transform: `scaleY(${0.3 + height / 150})`,
        }}
      />
    )), [bars])

  return (
    <div className="h-32 flex items-end justify-center space-x-1 bg-dark-800/50 rounded-lg p-4">
      {barElements}
    </div>
  )
}
