'use client'

import { useEffect, useState } from 'react'

interface AudioVisualizerProps {
  isActive: boolean
}

export default function AudioVisualizer({ isActive }: AudioVisualizerProps) {
  const [bars, setBars] = useState<number[]>(new Array(10).fill(0))

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isActive) {
      interval = setInterval(() => {
        setBars(prev => prev.map(() => Math.random() * 100))
      }, 200)
    } else {
      setBars(new Array(10).fill(0))
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isActive])

  return (
    <div className="h-32 flex items-end justify-center space-x-2 bg-gray-800 rounded p-4">
      {bars.map((height, index) => (
        <div
          key={index}
          className="w-4 bg-blue-500 rounded-t"
          style={{
            height: `${Math.max(height, 10)}%`,
          }}
        />
      ))}
    </div>
  )
}
