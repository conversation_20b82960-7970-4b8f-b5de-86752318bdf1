'use client'

import { useState, useCallback } from 'react'

interface Preset {
  id: string
  name: string
  description: string
  values: { [key: string]: number }
  category: 'default' | 'user'
  created?: string
}

interface PresetManagerProps {
  currentFrequencies: { [key: string]: number }
  onApplyPreset: (values: { [key: string]: number }) => void
  onSavePreset?: (preset: Preset) => void
}

const DEFAULT_PRESETS: Preset[] = [
  {
    id: 'flat',
    name: 'Plat',
    description: 'Aucun ajustement - son naturel',
    category: 'default',
    values: { '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0, '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0 }
  },
  {
    id: 'rock',
    name: 'Rock',
    description: 'Basses puissantes et aigus brillants',
    category: 'default',
    values: { '32Hz': 4, '64Hz': 3, '125Hz': 1, '250Hz': 0, '500Hz': -1, '1kHz': 0, '2kHz': 1, '4kHz': 3, '8kHz': 4, '16kHz': 3 }
  },
  {
    id: 'pop',
    name: 'Pop',
    description: 'Équilibré avec médiums présents',
    category: 'default',
    values: { '32Hz': 2, '64Hz': 1, '125Hz': 0, '250Hz': 1, '500Hz': 2, '1kHz': 2, '2kHz': 1, '4kHz': 0, '8kHz': 1, '16kHz': 1 }
  },
  {
    id: 'classical',
    name: 'Classique',
    description: 'Naturel avec aigus doux',
    category: 'default',
    values: { '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0, '1kHz': 0, '2kHz': -1, '4kHz': -1, '8kHz': 0, '16kHz': 2 }
  },
  {
    id: 'jazz',
    name: 'Jazz',
    description: 'Médiums chauds et basses contrôlées',
    category: 'default',
    values: { '32Hz': 2, '64Hz': 1, '125Hz': 1, '250Hz': 2, '500Hz': 1, '1kHz': 0, '2kHz': 0, '4kHz': -1, '8kHz': 0, '16kHz': 1 }
  },
  {
    id: 'electronic',
    name: 'Électronique',
    description: 'Basses profondes pour la musique électronique',
    category: 'default',
    values: { '32Hz': 5, '64Hz': 4, '125Hz': 2, '250Hz': 0, '500Hz': -1, '1kHz': 0, '2kHz': 1, '4kHz': 2, '8kHz': 1, '16kHz': 0 }
  },
  {
    id: 'vocal',
    name: 'Vocal',
    description: 'Optimisé pour les voix et podcasts',
    category: 'default',
    values: { '32Hz': -2, '64Hz': -1, '125Hz': 0, '250Hz': 3, '500Hz': 4, '1kHz': 4, '2kHz': 3, '4kHz': 1, '8kHz': 0, '16kHz': -1 }
  },
  {
    id: 'bass_boost',
    name: 'Bass Boost',
    description: 'Basses renforcées au maximum',
    category: 'default',
    values: { '32Hz': 6, '64Hz': 5, '125Hz': 3, '250Hz': 1, '500Hz': 0, '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0 }
  }
]

export default function PresetManager({ currentFrequencies, onApplyPreset, onSavePreset }: PresetManagerProps) {
  const [selectedPreset, setSelectedPreset] = useState<string>('flat')
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [newPresetName, setNewPresetName] = useState('')
  const [newPresetDescription, setNewPresetDescription] = useState('')
  const [userPresets, setUserPresets] = useState<Preset[]>([])

  const handleApplyPreset = useCallback((preset: Preset) => {
    setSelectedPreset(preset.id)
    onApplyPreset(preset.values)
  }, [onApplyPreset])

  const handleSavePreset = useCallback(() => {
    if (!newPresetName.trim()) return

    const newPreset: Preset = {
      id: `user_${Date.now()}`,
      name: newPresetName.trim(),
      description: newPresetDescription.trim() || 'Preset personnalisé',
      category: 'user',
      values: { ...currentFrequencies },
      created: new Date().toLocaleDateString()
    }

    setUserPresets(prev => [...prev, newPreset])
    
    if (onSavePreset) {
      onSavePreset(newPreset)
    }

    setNewPresetName('')
    setNewPresetDescription('')
    setShowSaveDialog(false)
    setSelectedPreset(newPreset.id)
  }, [newPresetName, newPresetDescription, currentFrequencies, onSavePreset])

  const handleDeletePreset = useCallback((presetId: string) => {
    setUserPresets(prev => prev.filter(p => p.id !== presetId))
    if (selectedPreset === presetId) {
      setSelectedPreset('flat')
    }
  }, [selectedPreset])

  const allPresets = [...DEFAULT_PRESETS, ...userPresets]

  return (
    <div className="card">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-white">Préréglages</h3>
        <button
          onClick={() => setShowSaveDialog(true)}
          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm"
        >
          💾 Sauvegarder
        </button>
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="mb-4 p-4 bg-gray-700 rounded border">
          <h4 className="text-white font-medium mb-3">Sauvegarder le preset actuel</h4>
          <div className="space-y-3">
            <input
              type="text"
              placeholder="Nom du preset"
              value={newPresetName}
              onChange={(e) => setNewPresetName(e.target.value)}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400"
            />
            <input
              type="text"
              placeholder="Description (optionnel)"
              value={newPresetDescription}
              onChange={(e) => setNewPresetDescription(e.target.value)}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400"
            />
            <div className="flex space-x-2">
              <button
                onClick={handleSavePreset}
                disabled={!newPresetName.trim()}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-white text-sm"
              >
                Sauvegarder
              </button>
              <button
                onClick={() => setShowSaveDialog(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded text-white text-sm"
              >
                Annuler
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Default Presets */}
      <div className="mb-6">
        <h4 className="text-white font-medium mb-3">Presets par défaut</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {DEFAULT_PRESETS.map((preset) => (
            <button
              key={preset.id}
              onClick={() => handleApplyPreset(preset)}
              className={`p-3 rounded text-left transition-colors ${
                selectedPreset === preset.id
                  ? 'bg-blue-600/20 border border-blue-600/40 text-blue-300'
                  : 'bg-gray-700 hover:bg-gray-600 text-white'
              }`}
            >
              <div className="font-medium text-sm">{preset.name}</div>
              <div className="text-xs text-gray-400 mt-1">{preset.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* User Presets */}
      {userPresets.length > 0 && (
        <div>
          <h4 className="text-white font-medium mb-3">Mes presets ({userPresets.length})</h4>
          <div className="space-y-2">
            {userPresets.map((preset) => (
              <div
                key={preset.id}
                className={`p-3 rounded border transition-colors ${
                  selectedPreset === preset.id
                    ? 'bg-blue-600/20 border-blue-600/40'
                    : 'bg-gray-700 border-gray-600'
                }`}
              >
                <div className="flex justify-between items-start">
                  <button
                    onClick={() => handleApplyPreset(preset)}
                    className="flex-1 text-left"
                  >
                    <div className="font-medium text-white text-sm">{preset.name}</div>
                    <div className="text-xs text-gray-400 mt-1">{preset.description}</div>
                    {preset.created && (
                      <div className="text-xs text-gray-500 mt-1">Créé le {preset.created}</div>
                    )}
                  </button>
                  <button
                    onClick={() => handleDeletePreset(preset.id)}
                    className="ml-2 p-1 text-red-400 hover:text-red-300 hover:bg-red-600/20 rounded"
                    title="Supprimer"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Current Values Preview */}
      <div className="mt-4 p-3 bg-gray-800 rounded">
        <h4 className="text-white font-medium mb-2 text-sm">Valeurs actuelles</h4>
        <div className="grid grid-cols-5 gap-2 text-xs">
          {Object.entries(currentFrequencies).map(([freq, value]) => (
            <div key={freq} className="text-center">
              <div className="text-gray-400">{freq}</div>
              <div className={`font-medium ${value > 0 ? 'text-green-400' : value < 0 ? 'text-red-400' : 'text-gray-300'}`}>
                {value > 0 ? '+' : ''}{value}dB
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
