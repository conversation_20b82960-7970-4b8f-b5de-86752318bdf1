# WaveCraft - Guide Audio Système

## 🎯 Fonctionnalités Implémentées

### ✅ **Capture Audio Système Complète**
- **3 modes de capture** : Microphone/Ligne, Audio Système, Mode Test
- **Traitement temps réel** : L'audio est capturé, traité par l'égaliseur, puis renvoyé
- **Chaîne audio complète** : Entrée → Volume → Égaliseur → Sortie
- **Monitoring en temps réel** : Niveaux audio et visualisation spectrale

### ✅ **Mode Microphone/Ligne (Recommandé)**
- **Capture microphone** : Accès via `getUserMedia()`
- **Entrée ligne** : Support des interfaces audio
- **Sélection de périphérique** : Choix de la source d'entrée
- **Monitoring niveau** : Prévention de la saturation
- **Sortie traitée** : Audio égalisé renvoyé aux haut-parleurs

### ✅ **Mode Audio Système (Expérimental)**
- **Capture d'écran audio** : Via `getDisplayMedia()` avec audio
- **Audio système complet** : YouTube, Spotify, jeux, etc.
- **Traitement transparent** : L'utilisateur entend l'audio égalisé
- **Support navigateur** : Chrome/Edge uniquement

### ✅ **Égaliseur Temps Réel**
- **10 bandes de fréquences** : 32Hz à 16kHz
- **Plage étendue** : -12dB à +12dB
- **Volume amplifiable** : 0% à 200% (avec amplification)
- **Presets intégrés** : Rock, Pop, Jazz, Classique, etc.
- **Réponse instantanée** : Changements appliqués en temps réel

## 🚀 Guide d'Utilisation

### **Mode 1 : Microphone/Ligne (Recommandé)**

#### **Configuration**
1. Connectez votre source audio :
   - **Microphone** : Pour podcasting, streaming, voix
   - **Interface audio** : Pour instruments, mixage
   - **Ligne d'entrée** : Pour sources externes

2. **Dans WaveCraft :**
   - Allez sur "Égaliseur"
   - Sélectionnez "Mode Microphone/Ligne"
   - Choisissez votre périphérique d'entrée
   - Cliquez "Démarrer Capture"

#### **Utilisation**
- L'audio de votre microphone/source est capturé
- Il passe par l'égaliseur en temps réel
- Il est renvoyé aux haut-parleurs/casque
- Ajustez l'égaliseur pendant que vous parlez/jouez

#### **Cas d'usage**
- 🎙️ **Podcasting** : Améliorer la voix en direct
- 🎸 **Instruments** : Égaliser guitare, piano, etc.
- 🎮 **Streaming** : Optimiser l'audio pour Twitch/YouTube
- 🎵 **Musique live** : Traitement en temps réel

### **Mode 2 : Audio Système (Expérimental)**

#### **Configuration**
1. **Navigateur requis** : Chrome ou Edge (pas Firefox)
2. **Dans WaveCraft :**
   - Sélectionnez "Mode Audio Système"
   - Cliquez "Capturer Audio Système"
   - Dans la popup, sélectionnez "Partager l'audio système"

#### **Utilisation**
- Tout l'audio de votre système est capturé
- YouTube, Spotify, jeux, notifications, etc.
- L'audio passe par l'égaliseur
- Vous entendez la version égalisée

#### **Cas d'usage**
- 🎬 **Vidéos** : Améliorer l'audio de YouTube, Netflix
- 🎵 **Musique** : Égaliser Spotify, Apple Music
- 🎮 **Jeux** : Optimiser l'audio des jeux
- 📺 **Streaming** : Améliorer tout l'audio système

#### **⚠️ Précautions**
- **Utilisez un casque** pour éviter les boucles audio
- **Baissez le volume** si vous entendez de l'écho
- **Fermez les autres onglets** audio pour réduire la latence

### **Mode 3 : Test**
- Générateur de sons intégré
- Parfait pour tester et calibrer l'égaliseur
- Aucune source externe requise

## 🔧 Architecture Technique

### **Chaîne Audio Complète**
```
Source Audio → MediaStream → AudioContext → GainNode → EQ Filters → AnalyserNode → Destination
     ↓              ↓            ↓           ↓           ↓            ↓            ↓
Micro/Système → Capture → Web Audio → Volume → Égaliseur → Analyse → Haut-parleurs
```

### **Composants Principaux**
- **`useSystemAudio`** : Hook pour capture microphone/ligne
- **`SystemEqualizer`** : Interface complète avec sélection de périphérique
- **`ScreenAudioCapture`** : Capture audio système via getDisplayMedia
- **`CompleteEqualizer`** : Interface unifiée avec sélection de mode

### **Technologies Utilisées**
- **`getUserMedia()`** : Capture microphone/ligne
- **`getDisplayMedia()`** : Capture audio système
- **Web Audio API** : Traitement audio temps réel
- **MediaStream API** : Gestion des flux audio

## 🎛️ Fonctionnalités Avancées

### **Monitoring Audio**
- **Niveau d'entrée** : Barre de niveau en temps réel
- **Prévention saturation** : Indicateur rouge si trop fort
- **Visualiseur spectral** : 10 barres de fréquences
- **Métriques temps réel** : RMS, peak, etc.

### **Contrôles Avancés**
- **Volume amplifiable** : Jusqu'à 200% pour sources faibles
- **Égaliseur précis** : Résolution 1dB, plage ±12dB
- **Presets intelligents** : Optimisés par type de contenu
- **Reset instantané** : Retour aux réglages neutres

### **Gestion des Périphériques**
- **Énumération automatique** : Liste tous les périphériques audio
- **Sélection dynamique** : Changement sans redémarrage
- **Gestion des erreurs** : Messages d'erreur explicites
- **Permissions** : Gestion des autorisations navigateur

## 🐛 Dépannage

### **Problèmes Courants**

#### **"Pas d'autorisation microphone"**
- Cliquez sur l'icône 🔒 dans la barre d'adresse
- Autorisez l'accès au microphone
- Rechargez la page

#### **"Pas de son en sortie"**
- Vérifiez que le volume n'est pas à 0%
- Vérifiez les paramètres audio système
- Testez avec un casque

#### **"Écho ou boucle audio"**
- **Utilisez un casque** (solution recommandée)
- Baissez le volume de sortie
- Éloignez le microphone des haut-parleurs

#### **"Latence trop élevée"**
- Fermez les autres onglets avec audio
- Utilisez Chrome plutôt que Firefox
- Réduisez la taille du buffer (si disponible)

#### **"Audio système ne fonctionne pas"**
- Utilisez Chrome ou Edge (pas Firefox)
- Assurez-vous de sélectionner "Partager l'audio système"
- Vérifiez que l'onglet a l'autorisation

### **Messages d'Erreur**

| Erreur | Cause | Solution |
|--------|-------|----------|
| `NotAllowedError` | Permission refusée | Autoriser microphone dans navigateur |
| `NotFoundError` | Pas de périphérique | Vérifier connexion micro/interface |
| `NotSupportedError` | Navigateur incompatible | Utiliser Chrome/Edge |
| `AbortError` | Capture interrompue | Redémarrer la capture |

## 📊 Performances

### **Latence**
- **Mode Microphone** : ~10-20ms (excellent)
- **Mode Système** : ~20-50ms (bon)
- **Facteurs** : Navigateur, système, buffer size

### **Qualité Audio**
- **Fréquence d'échantillonnage** : 44.1kHz (CD quality)
- **Résolution** : 16-bit minimum
- **Canaux** : Stéréo (2 canaux)
- **Bande passante** : 20Hz - 20kHz

### **Compatibilité**

| Navigateur | Microphone | Audio Système | Mobile |
|------------|------------|---------------|---------|
| Chrome | ✅ Excellent | ✅ Excellent | ✅ Bon |
| Edge | ✅ Excellent | ✅ Excellent | ❌ Non |
| Firefox | ✅ Bon | ❌ Non supporté | ✅ Limité |
| Safari | ⚠️ Limité | ❌ Non supporté | ⚠️ Limité |

## 🎯 Cas d'Usage Réels

### **Podcasting/Streaming**
```
Microphone → WaveCraft → OBS/Streaming Software
```
- Égaliseur en temps réel pour la voix
- Monitoring du niveau audio
- Presets optimisés pour la parole

### **Production Musicale**
```
Instrument → Interface Audio → WaveCraft → DAW
```
- Traitement en temps réel des instruments
- Égaliseur créatif pendant l'enregistrement
- Monitoring précis des fréquences

### **Gaming/Divertissement**
```
Audio Système → WaveCraft → Casque
```
- Amélioration de l'audio des jeux
- Égaliseur pour films/séries
- Optimisation pour différents genres

L'égaliseur WaveCraft est maintenant **100% fonctionnel** pour le traitement audio système en temps réel ! 🎵🎛️
