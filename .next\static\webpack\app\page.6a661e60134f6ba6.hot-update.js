"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AudioVisualizer.tsx":
/*!********************************************!*\
  !*** ./src/components/AudioVisualizer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioVisualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AudioVisualizer(param) {\n    let { isActive } = param;\n    _s();\n    const [bars, setBars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(10).fill(0));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioVisualizer.useEffect\": ()=>{\n            let interval;\n            if (isActive) {\n                interval = setInterval({\n                    \"AudioVisualizer.useEffect\": ()=>{\n                        setBars({\n                            \"AudioVisualizer.useEffect\": (prev)=>prev.map({\n                                    \"AudioVisualizer.useEffect\": ()=>Math.random() * 100\n                                }[\"AudioVisualizer.useEffect\"])\n                        }[\"AudioVisualizer.useEffect\"]);\n                    }\n                }[\"AudioVisualizer.useEffect\"], 200);\n            } else {\n                setBars(new Array(10).fill(0));\n            }\n            return ({\n                \"AudioVisualizer.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"AudioVisualizer.useEffect\"];\n        }\n    }[\"AudioVisualizer.useEffect\"], [\n        isActive\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-32 flex items-end justify-center space-x-2 bg-gray-800 rounded p-4\",\n        children: bars.map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 bg-blue-500 rounded-t\",\n                style: {\n                    height: \"\".concat(Math.max(height, 10), \"%\")\n                }\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioVisualizer, \"m1GluPD9rLJVrZl+fz9TpMS3jUU=\");\n_c = AudioVisualizer;\nvar _c;\n$RefreshReg$(_c, \"AudioVisualizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioVisualizer.tsx\n"));

/***/ })

});