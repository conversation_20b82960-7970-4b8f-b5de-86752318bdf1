'use client'

import { useState } from 'react'

export default function History() {
  const [selectedPeriod, setSelectedPeriod] = useState('week')
  
  const historyData = [
    {
      id: 1,
      timestamp: '2024-01-15 14:32',
      track: 'Bohemian Rhapsody',
      artist: 'Queen',
      genre: 'Rock',
      confidence: 95,
      adjustments: 'Basses +3dB, Aigus +2dB',
      duration: '5:55'
    },
    {
      id: 2,
      timestamp: '2024-01-15 14:26',
      track: '<PERSON> Jean',
      artist: '<PERSON>',
      genre: 'Pop',
      confidence: 92,
      adjustments: 'Médiums +1dB, Voix +2dB',
      duration: '4:54'
    },
    {
      id: 3,
      timestamp: '2024-01-15 14:21',
      track: 'Hotel California',
      artist: 'Eagles',
      genre: 'Rock',
      confidence: 88,
      adjustments: 'Basses +2dB, Aigus +1dB',
      duration: '6:30'
    },
    {
      id: 4,
      timestamp: '2024-01-15 14:14',
      track: 'Imagine',
      artist: '<PERSON>',
      genre: 'Folk',
      confidence: 90,
      adjustments: 'Naturel, Voix +1dB',
      duration: '3:07'
    }
  ]

  const genreStats = [
    { genre: 'Rock', count: 45, percentage: 35, color: 'bg-red-500' },
    { genre: 'Pop', count: 32, percentage: 25, color: 'bg-blue-500' },
    { genre: 'Jazz', count: 20, percentage: 15, color: 'bg-yellow-500' },
    { genre: 'Classique', count: 16, percentage: 12, color: 'bg-purple-500' },
    { genre: 'Électronique', count: 10, percentage: 8, color: 'bg-green-500' },
    { genre: 'Autres', count: 6, percentage: 5, color: 'bg-gray-500' }
  ]

  const periods = [
    { id: 'day', label: 'Aujourd\'hui' },
    { id: 'week', label: 'Cette semaine' },
    { id: 'month', label: 'Ce mois' },
    { id: 'year', label: 'Cette année' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Historique & Statistiques</h1>
          <p className="text-gray-400">Analysez vos habitudes d'écoute</p>
        </div>
        
        {/* Period Selector */}
        <div className="flex space-x-2">
          {periods.map((period) => (
            <button
              key={period.id}
              onClick={() => setSelectedPeriod(period.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedPeriod === period.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-dark-700 text-gray-300 hover:bg-dark-600'
              }`}
            >
              {period.label}
            </button>
          ))}
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">🎵</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">129</p>
              <p className="text-gray-400 text-sm">Morceaux écoutés</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">⏱️</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">47h</p>
              <p className="text-gray-400 text-sm">Temps d'écoute</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">🎭</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">23</p>
              <p className="text-gray-400 text-sm">Genres différents</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-600/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">🧠</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">94.2%</p>
              <p className="text-gray-400 text-sm">Précision IA</p>
            </div>
          </div>
        </div>
      </div>

      {/* Genre Distribution */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Répartition par Genre</h3>
        <div className="space-y-4">
          {genreStats.map((stat, index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-20 text-sm text-gray-300">{stat.genre}</div>
              <div className="flex-1 bg-dark-700 rounded-full h-3 relative">
                <div 
                  className={`${stat.color} h-full rounded-full transition-all duration-500`}
                  style={{ width: `${stat.percentage}%` }}
                />
              </div>
              <div className="w-16 text-sm text-white text-right">{stat.count}</div>
              <div className="w-12 text-sm text-gray-400 text-right">{stat.percentage}%</div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent History */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Historique Récent</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-dark-600">
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Heure</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Morceau</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Genre</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Confiance</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Ajustements</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Durée</th>
              </tr>
            </thead>
            <tbody>
              {historyData.map((item) => (
                <tr key={item.id} className="border-b border-dark-700/50 hover:bg-dark-700/30">
                  <td className="py-3 px-4 text-gray-300 text-sm">
                    {item.timestamp.split(' ')[1]}
                  </td>
                  <td className="py-3 px-4">
                    <div>
                      <div className="text-white font-medium">{item.track}</div>
                      <div className="text-gray-400 text-sm">{item.artist}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-primary-600/20 text-primary-300 rounded text-sm">
                      {item.genre}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-dark-600 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-full rounded-full"
                          style={{ width: `${item.confidence}%` }}
                        />
                      </div>
                      <span className="text-white text-sm">{item.confidence}%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-300 text-sm">{item.adjustments}</td>
                  <td className="py-3 px-4 text-gray-300 text-sm">{item.duration}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Export Options */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Exporter les Données</h3>
        <div className="flex space-x-4">
          <button className="btn-primary">
            📊 Exporter CSV
          </button>
          <button className="btn-secondary">
            📈 Rapport PDF
          </button>
          <button className="btn-secondary">
            📋 Copier les statistiques
          </button>
        </div>
      </div>
    </div>
  )
}
