"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        audioContext: null,\n        gainNode: null,\n        analyserNode: null,\n        eqNodes: [],\n        isInitialized: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    });\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const oscillatorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fréquences de l'égaliseur\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                console.log('Initializing audio context...');\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Reprendre le contexte si suspendu\n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully', {\n                    state: audioContext.state,\n                    sampleRate: audioContext.sampleRate\n                });\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], [\n        state.isInitialized\n    ]);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            console.log('Setting volume to:', volume);\n            if (!state.gainNode || !state.audioContext) {\n                console.log('No gain node or audio context available');\n                return;\n            }\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            try {\n                state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n                console.log('Volume set successfully:', gainValue);\n            } catch (error) {\n                console.error('Error setting volume:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex]) return;\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    // Initialiser automatiquement au montage\n    useEffect({\n        \"useAudioContext.useEffect\": ()=>{\n            initializeAudioContext();\n        }\n    }[\"useAudioContext.useEffect\"], [\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});