"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AudioVisualizer.tsx":
/*!********************************************!*\
  !*** ./src/components/AudioVisualizer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioVisualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AudioVisualizer(param) {\n    let { isActive } = param;\n    _s();\n    const [bars, setBars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(15).fill(0));\n    const updateBars = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AudioVisualizer.useCallback[updateBars]\": ()=>{\n            setBars({\n                \"AudioVisualizer.useCallback[updateBars]\": (prev)=>prev.map({\n                        \"AudioVisualizer.useCallback[updateBars]\": ()=>Math.random() * 100\n                    }[\"AudioVisualizer.useCallback[updateBars]\"])\n            }[\"AudioVisualizer.useCallback[updateBars]\"]);\n        }\n    }[\"AudioVisualizer.useCallback[updateBars]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioVisualizer.useEffect\": ()=>{\n            let interval;\n            if (isActive) {\n                interval = setInterval(updateBars, 80) // Plus rapide\n                ;\n            } else {\n                setBars(new Array(15).fill(0));\n            }\n            return ({\n                \"AudioVisualizer.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"AudioVisualizer.useEffect\"];\n        }\n    }[\"AudioVisualizer.useEffect\"], [\n        isActive,\n        updateBars\n    ]);\n    const barElements = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AudioVisualizer.useMemo[barElements]\": ()=>bars.map({\n                \"AudioVisualizer.useMemo[barElements]\": (height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 bg-gradient-to-t from-primary-600 to-primary-400 rounded-t transition-all duration-75\",\n                        style: {\n                            height: \"\".concat(Math.max(height, 8), \"%\"),\n                            transform: \"scaleY(\".concat(0.3 + height / 150, \")\")\n                        }\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 7\n                    }, this)\n            }[\"AudioVisualizer.useMemo[barElements]\"])\n    }[\"AudioVisualizer.useMemo[barElements]\"], [\n        bars\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-32 flex items-end justify-center space-x-1 bg-dark-800/50 rounded-lg p-4\",\n        children: barElements\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioVisualizer, \"mnYijmCvuMW4Z/hHTZYAFIjHVJU=\");\n_c = AudioVisualizer;\nvar _c;\n$RefreshReg$(_c, \"AudioVisualizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioVisualizer.tsx\n"));

/***/ })

});