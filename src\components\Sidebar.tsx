'use client'

import { useState } from 'react'

interface SidebarProps {
  activeTab: string
  setActiveTab: (tab: string) => void
}

const menuItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '🏠',
    description: 'Vue d\'ensemble'
  },
  {
    id: 'equalizer',
    label: 'Égaliseur',
    icon: '🎛️',
    description: 'Contrôles audio'
  },
  {
    id: 'ai-analysis',
    label: 'Analyse IA',
    icon: '🧠',
    description: 'Intelligence artificielle'
  },
  {
    id: 'history',
    label: 'Historique',
    icon: '📊',
    description: 'Statistiques'
  },
  {
    id: 'learning',
    label: 'Apprentissage',
    icon: '🎓',
    description: 'Entraînement IA'
  },
  {
    id: 'settings',
    label: 'Paramètres',
    icon: '⚙️',
    description: 'Configuration'
  }
]

export default function Sidebar({ activeTab, setActiveTab }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className={`${isCollapsed ? 'w-20' : 'w-64'} bg-gray-800 border-r border-gray-700`}>
      <div className="p-6">
        {/* Logo */}
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-xl font-bold">🎵</span>
          </div>
          {!isCollapsed && (
            <div>
              <h1 className="text-xl font-bold text-white">WaveCraft</h1>
              <p className="text-sm text-gray-400">Audio AI</p>
            </div>
          )}
        </div>

        {/* Toggle Button */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="mb-6 p-2 rounded bg-gray-700 hover:bg-gray-600"
        >
          <span className="text-lg">{isCollapsed ? '→' : '←'}</span>
        </button>

        {/* Menu Items */}
        <nav className="space-y-2">
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`nav-item w-full text-left ${
                activeTab === item.id ? 'active' : ''
              }`}
              title={isCollapsed ? item.label : ''}
            >
              <span className="text-xl">{item.icon}</span>
              {!isCollapsed && (
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className="text-sm text-gray-400">{item.description}</div>
                </div>
              )}
            </button>
          ))}
        </nav>

        {/* Status Indicator */}
        {!isCollapsed && (
          <div className="mt-8 p-4 card">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">Statut</span>
            </div>
            <p className="text-xs text-gray-400">IA en écoute</p>
            <p className="text-xs text-gray-400">Audio détecté</p>
          </div>
        )}
      </div>
    </div>
  )
}
