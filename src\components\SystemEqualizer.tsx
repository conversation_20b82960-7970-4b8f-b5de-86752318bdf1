'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { useSystemAudio } from '@/hooks/useSystemAudio'
import AudioVisualizerNew from './AudioVisualizerNew'

const PRESETS = {
  flat: { name: 'Plat', values: { '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0, '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0 } },
  rock: { name: 'Rock', values: { '32Hz': 4, '64Hz': 3, '125Hz': 1, '250Hz': 0, '500Hz': -1, '1kHz': 0, '2kHz': 1, '4kHz': 3, '8kHz': 4, '16kHz': 3 } },
  pop: { name: 'Pop', values: { '32Hz': 2, '64Hz': 1, '125Hz': 0, '250Hz': 1, '500Hz': 2, '1kHz': 2, '2kHz': 1, '4kHz': 0, '8kHz': 1, '16kHz': 1 } },
  classical: { name: 'Classique', values: { '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0, '1kHz': 0, '2kHz': -1, '4kHz': -1, '8kHz': 0, '16kHz': 2 } },
  jazz: { name: 'Jazz', values: { '32Hz': 2, '64Hz': 1, '125Hz': 1, '250Hz': 2, '500Hz': 1, '1kHz': 0, '2kHz': 0, '4kHz': -1, '8kHz': 0, '16kHz': 1 } },
  electronic: { name: 'Électronique', values: { '32Hz': 5, '64Hz': 4, '125Hz': 2, '250Hz': 0, '500Hz': -1, '1kHz': 0, '2kHz': 1, '4kHz': 2, '8kHz': 1, '16kHz': 0 } },
  vocal: { name: 'Vocal', values: { '32Hz': -2, '64Hz': -1, '125Hz': 0, '250Hz': 3, '500Hz': 4, '1kHz': 4, '2kHz': 3, '4kHz': 1, '8kHz': 0, '16kHz': -1 } }
}

export default function SystemEqualizer() {
  const [selectedPreset, setSelectedPreset] = useState('flat')
  const outputAudioRef = useRef<HTMLAudioElement>(null)
  
  const {
    isCapturing,
    isInitialized,
    volume,
    frequencies,
    error,
    inputDevices,
    selectedInputDevice,
    audioLevel,
    outputStream,
    initializeAudio,
    startCapture,
    stopCapture,
    setVolume,
    setEQFrequency,
    resetEQ,
    getAnalyserData,
    setInputDevice
  } = useSystemAudio()

  // Connecter le stream de sortie à l'élément audio pour la lecture
  useEffect(() => {
    if (outputStream && outputAudioRef.current) {
      outputAudioRef.current.srcObject = outputStream
      outputAudioRef.current.play().catch(console.error)
    }
  }, [outputStream])

  const handleVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value)
    setVolume(newVolume)
  }, [setVolume])

  const handleFrequencyChange = useCallback((freq: string, value: number) => {
    setEQFrequency(freq, value)
    setSelectedPreset('custom')
  }, [setEQFrequency])

  const handlePresetChange = useCallback((presetKey: string) => {
    const preset = PRESETS[presetKey as keyof typeof PRESETS]
    if (!preset) return

    setSelectedPreset(presetKey)
    Object.entries(preset.values).forEach(([freq, gain]) => {
      setEQFrequency(freq, gain)
    })
  }, [setEQFrequency])

  const handleReset = useCallback(() => {
    resetEQ()
    setSelectedPreset('flat')
  }, [resetEQ])

  const handleDeviceChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setInputDevice(e.target.value)
  }, [setInputDevice])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Égaliseur Audio Système</h1>
        <p className="text-gray-400">Traitement audio en temps réel de votre système</p>
        
        {/* Status */}
        <div className="mt-4 flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded ${
            isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isInitialized ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
            <span className="text-sm">
              {isInitialized ? 'Audio prêt' : 'Non initialisé'}
            </span>
          </div>
          
          <div className={`flex items-center space-x-2 px-3 py-1 rounded ${
            isCapturing ? 'bg-blue-600/20 text-blue-300' : 'bg-gray-600/20 text-gray-300'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isCapturing ? 'bg-blue-500' : 'bg-gray-500'}`}></div>
            <span className="text-sm">
              {isCapturing ? 'Capture active' : 'Capture arrêtée'}
            </span>
          </div>
          
          {!isInitialized && (
            <button
              onClick={initializeAudio}
              className="px-4 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm"
            >
              Initialiser Audio
            </button>
          )}
        </div>

        {error && (
          <div className="mt-2 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm">
            ❌ {error}
          </div>
        )}
      </div>

      {/* Audio Input Selection */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Source Audio</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Périphérique d'entrée
            </label>
            <select
              value={selectedInputDevice}
              onChange={handleDeviceChange}
              disabled={isCapturing}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white disabled:opacity-50"
            >
              {inputDevices.map((device) => (
                <option key={device.deviceId} value={device.deviceId}>
                  {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={isCapturing ? stopCapture : startCapture}
              disabled={!isInitialized}
              className={`px-6 py-3 rounded font-medium disabled:opacity-50 disabled:cursor-not-allowed ${
                isCapturing 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isCapturing ? '⏹️ Arrêter Capture' : '🎤 Démarrer Capture'}
            </button>
            
            {/* Audio Level Meter */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Niveau:</span>
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-full rounded-full transition-all duration-100 ${
                      audioLevel > 80 ? 'bg-red-500' : 
                      audioLevel > 50 ? 'bg-yellow-500' : 
                      'bg-green-500'
                    }`}
                    style={{ width: `${audioLevel}%` }}
                  />
                </div>
                <span className="text-sm text-white w-12">{Math.round(audioLevel)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Volume Control */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Volume Principal</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-4">
            <span className="text-2xl">🔊</span>
            <input
              type="range"
              min="0"
              max="200"
              value={volume}
              onChange={handleVolumeChange}
              disabled={!isInitialized}
              className="flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer disabled:cursor-not-allowed"
            />
            <span className="text-white font-medium w-16">{volume}%</span>
          </div>
          <div className="text-xs text-gray-400">
            Volume système : {volume}% {volume === 0 ? '(Muet)' : volume > 100 ? '(Amplifié)' : ''}
          </div>
        </div>
      </div>

      {/* Presets */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Préréglages</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {Object.entries(PRESETS).map(([key, preset]) => (
            <button
              key={key}
              onClick={() => handlePresetChange(key)}
              disabled={!isInitialized}
              className={`p-3 rounded text-left transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                selectedPreset === key
                  ? 'bg-blue-600/20 border border-blue-600/40 text-blue-300'
                  : 'bg-gray-700 hover:bg-gray-600 text-white'
              }`}
            >
              <div className="font-medium text-sm">{preset.name}</div>
            </button>
          ))}
          <button
            onClick={handleReset}
            disabled={!isInitialized}
            className="p-3 rounded text-left bg-gray-700 hover:bg-gray-600 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="font-medium text-sm">🔄 Reset</div>
          </button>
        </div>
      </div>

      {/* Equalizer */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-6">Égaliseur 10 Bandes</h3>
        <div className="grid grid-cols-5 md:grid-cols-10 gap-4">
          {Object.entries(frequencies).map(([freq, value]) => (
            <div key={freq} className="flex flex-col items-center space-y-2">
              <div className="text-xs text-gray-400 font-medium">{freq}</div>
              <div className="h-32 w-8 bg-gray-700 rounded relative">
                <input
                  type="range"
                  min="-12"
                  max="12"
                  value={value}
                  onChange={(e) => handleFrequencyChange(freq, parseInt(e.target.value))}
                  disabled={!isInitialized}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                  style={{ writingMode: 'bt-lr', WebkitAppearance: 'slider-vertical' }}
                />
                <div 
                  className={`absolute bottom-0 w-full rounded transition-all ${
                    value >= 0 ? 'bg-blue-500' : 'bg-red-500'
                  }`}
                  style={{ 
                    height: `${50 + (value * 2)}%`,
                    minHeight: '4px'
                  }}
                />
                <div 
                  className="absolute w-6 h-2 bg-white rounded shadow-lg"
                  style={{ 
                    bottom: `${50 + (value * 2) - 4}%`,
                    left: '50%',
                    transform: 'translateX(-50%)'
                  }}
                />
              </div>
              <div className="text-xs text-white font-medium">
                {value > 0 ? '+' : ''}{value}dB
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Audio Visualizer */}
      <AudioVisualizerNew 
        isActive={isCapturing} 
        getAnalyserData={getAnalyserData}
        title="Visualisation Audio Système"
      />

      {/* Instructions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Instructions</h3>
        <div className="space-y-2 text-sm text-gray-300">
          <p>• <strong>Initialiser :</strong> Cliquez sur "Initialiser Audio" pour préparer le système</p>
          <p>• <strong>Périphérique :</strong> Sélectionnez votre source audio (microphone, ligne d'entrée)</p>
          <p>• <strong>Capture :</strong> Cliquez sur "Démarrer Capture" pour commencer le traitement</p>
          <p>• <strong>Volume :</strong> Ajustez le volume de sortie (0-200% avec amplification)</p>
          <p>• <strong>Égaliseur :</strong> Modifiez les fréquences en temps réel</p>
          <p>• <strong>Niveau :</strong> Surveillez le niveau d'entrée pour éviter la saturation</p>
        </div>
      </div>

      {/* Hidden audio element for output */}
      <audio
        ref={outputAudioRef}
        autoPlay
        muted={false}
        style={{ display: 'none' }}
      />
    </div>
  )
}
