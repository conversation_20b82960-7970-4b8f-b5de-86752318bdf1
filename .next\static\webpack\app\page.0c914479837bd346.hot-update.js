"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardNew */ \"(app-pages-browser)/./src/components/DashboardNew.tsx\");\n/* harmony import */ var _components_CompleteEqualizer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CompleteEqualizer */ \"(app-pages-browser)/./src/components/CompleteEqualizer.tsx\");\n/* harmony import */ var _components_AIAnalysis__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AIAnalysis */ \"(app-pages-browser)/./src/components/AIAnalysis.tsx\");\n/* harmony import */ var _components_History__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/History */ \"(app-pages-browser)/./src/components/History.tsx\");\n/* harmony import */ var _components_Settings__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _components_Learning__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Learning */ \"(app-pages-browser)/./src/components/Learning.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n        }\n    }[\"Home.useCallback[handleTabChange]\"], []);\n    const renderContent = ()=>{\n        switch(activeTab){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 16\n                }, this);\n            case 'equalizer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompleteEqualizer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 16\n                }, this);\n            case 'ai-analysis':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAnalysis__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 16\n                }, this);\n            case 'history':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_History__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Settings__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, this);\n            case 'learning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Learning__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardNew__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                activeTab: activeTab,\n                setActiveTab: handleTabChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: renderContent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"nYYhcld3/ncl/1UkEalOJyXGeRY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUU2QztBQUNIO0FBQ1U7QUFDVTtBQUNkO0FBQ047QUFDRTtBQUNBO0FBRTdCLFNBQVNTOztJQUN0QixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1gsK0NBQVFBLENBQUM7SUFFM0MsTUFBTVksa0JBQWtCWCxrREFBV0E7NkNBQUMsQ0FBQ1k7WUFDbkNGLGFBQWFFO1FBQ2Y7NENBQUcsRUFBRTtJQUVMLE1BQU1DLGdCQUFnQjtRQUNwQixPQUFRSjtZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNQLGdFQUFZQTs7Ozs7WUFDdEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0MscUVBQWlCQTs7Ozs7WUFDM0IsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0MsOERBQVVBOzs7OztZQUNwQixLQUFLO2dCQUNILHFCQUFPLDhEQUFDQywyREFBT0E7Ozs7O1lBQ2pCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNDLDREQUFRQTs7Ozs7WUFDbEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0MsNERBQVFBOzs7OztZQUNsQjtnQkFDRSxxQkFBTyw4REFBQ0wsZ0VBQVlBOzs7OztRQUN4QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNZO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDZCwyREFBT0E7Z0JBQUNRLFdBQVdBO2dCQUFXQyxjQUFjQzs7Ozs7OzBCQUM3Qyw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNaRjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWDtHQXBDd0JMO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsYSBLYWxib3Vzc2lcXERlc2t0b3BcXEV2ZXJ5XFxmcFxcZWdhbGlzZXVyXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IFNpZGViYXIgZnJvbSAnQC9jb21wb25lbnRzL1NpZGViYXInXG5pbXBvcnQgRGFzaGJvYXJkTmV3IGZyb20gJ0AvY29tcG9uZW50cy9EYXNoYm9hcmROZXcnXG5pbXBvcnQgQ29tcGxldGVFcXVhbGl6ZXIgZnJvbSAnQC9jb21wb25lbnRzL0NvbXBsZXRlRXF1YWxpemVyJ1xuaW1wb3J0IEFJQW5hbHlzaXMgZnJvbSAnQC9jb21wb25lbnRzL0FJQW5hbHlzaXMnXG5pbXBvcnQgSGlzdG9yeSBmcm9tICdAL2NvbXBvbmVudHMvSGlzdG9yeSdcbmltcG9ydCBTZXR0aW5ncyBmcm9tICdAL2NvbXBvbmVudHMvU2V0dGluZ3MnXG5pbXBvcnQgTGVhcm5pbmcgZnJvbSAnQC9jb21wb25lbnRzL0xlYXJuaW5nJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ2Rhc2hib2FyZCcpXG5cbiAgY29uc3QgaGFuZGxlVGFiQ2hhbmdlID0gdXNlQ2FsbGJhY2soKHRhYjogc3RyaW5nKSA9PiB7XG4gICAgc2V0QWN0aXZlVGFiKHRhYilcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVuZGVyQ29udGVudCA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKGFjdGl2ZVRhYikge1xuICAgICAgY2FzZSAnZGFzaGJvYXJkJzpcbiAgICAgICAgcmV0dXJuIDxEYXNoYm9hcmROZXcgLz5cbiAgICAgIGNhc2UgJ2VxdWFsaXplcic6XG4gICAgICAgIHJldHVybiA8Q29tcGxldGVFcXVhbGl6ZXIgLz5cbiAgICAgIGNhc2UgJ2FpLWFuYWx5c2lzJzpcbiAgICAgICAgcmV0dXJuIDxBSUFuYWx5c2lzIC8+XG4gICAgICBjYXNlICdoaXN0b3J5JzpcbiAgICAgICAgcmV0dXJuIDxIaXN0b3J5IC8+XG4gICAgICBjYXNlICdzZXR0aW5ncyc6XG4gICAgICAgIHJldHVybiA8U2V0dGluZ3MgLz5cbiAgICAgIGNhc2UgJ2xlYXJuaW5nJzpcbiAgICAgICAgcmV0dXJuIDxMZWFybmluZyAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxEYXNoYm9hcmROZXcgLz5cbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIDxTaWRlYmFyIGFjdGl2ZVRhYj17YWN0aXZlVGFifSBzZXRBY3RpdmVUYWI9e2hhbmRsZVRhYkNoYW5nZX0gLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICB7cmVuZGVyQ29udGVudCgpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJTaWRlYmFyIiwiRGFzaGJvYXJkTmV3IiwiQ29tcGxldGVFcXVhbGl6ZXIiLCJBSUFuYWx5c2lzIiwiSGlzdG9yeSIsIlNldHRpbmdzIiwiTGVhcm5pbmciLCJIb21lIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaGFuZGxlVGFiQ2hhbmdlIiwidGFiIiwicmVuZGVyQ29udGVudCIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CompleteEqualizer.tsx":
/*!**********************************************!*\
  !*** ./src/components/CompleteEqualizer.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CompleteEqualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SystemEqualizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SystemEqualizer */ \"(app-pages-browser)/./src/components/SystemEqualizer.tsx\");\n/* harmony import */ var _ScreenAudioCapture__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ScreenAudioCapture */ \"(app-pages-browser)/./src/components/ScreenAudioCapture.tsx\");\n/* harmony import */ var _EqualizerNew__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EqualizerNew */ \"(app-pages-browser)/./src/components/EqualizerNew.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CompleteEqualizer() {\n    var _modes_find;\n    _s();\n    const [activeMode, setActiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('microphone');\n    const modes = [\n        {\n            id: 'microphone',\n            name: 'Microphone/Ligne',\n            icon: '🎤',\n            description: 'Capture audio depuis microphone ou entrée ligne',\n            recommended: true\n        },\n        {\n            id: 'screen',\n            name: 'Audio Système',\n            icon: '🖥️',\n            description: 'Capture l\\'audio système complet (expérimental)',\n            recommended: false\n        },\n        {\n            id: 'test',\n            name: 'Mode Test',\n            icon: '🔊',\n            description: 'Générateur de sons pour tester l\\'égaliseur',\n            recommended: false\n        }\n    ];\n    const renderContent = ()=>{\n        switch(activeMode){\n            case 'microphone':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemEqualizer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, this);\n            case 'screen':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScreenAudioCapture__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case 'test':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EqualizerNew__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemEqualizer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"WaveCraft - \\xc9galiseur Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Traitement audio en temps r\\xe9el avec intelligence artificielle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Mode de Capture Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: modes.map((mode)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveMode(mode.id),\n                                className: \"p-4 rounded-lg border-2 transition-all text-left \".concat(activeMode === mode.id ? 'border-blue-500 bg-blue-600/10' : 'border-gray-600 bg-gray-700/50 hover:border-gray-500'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: mode.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-white\",\n                                                            children: mode.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        mode.recommended && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs\",\n                                                            children: \"Recommand\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mt-1\",\n                                                    children: mode.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, mode.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: [\n                            \"Instructions - \",\n                            (_modes_find = modes.find((m)=>m.id === activeMode)) === null || _modes_find === void 0 ? void 0 : _modes_find.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 text-sm text-gray-300\",\n                        children: [\n                            activeMode === 'microphone' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-600/10 border border-blue-600/20 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium mb-2\",\n                                                children: \"\\uD83C\\uDFA4 Mode Microphone/Ligne (Recommand\\xe9)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Connectez votre microphone ou source audio \\xe0 l'entr\\xe9e ligne\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• S\\xe9lectionnez le bon p\\xe9riph\\xe9rique d'entr\\xe9e\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: '• Cliquez sur \"D\\xe9marrer Capture\" pour commencer'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• L'audio sera trait\\xe9 en temps r\\xe9el et renvoy\\xe9 aux haut-parleurs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Ajustez l'\\xe9galiseur pendant la lecture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Cas d'usage :\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Podcasting, streaming, musique live, instruments\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            activeMode === 'screen' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-yellow-600/10 border border-yellow-600/20 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-300 font-medium mb-2\",\n                                                children: \"\\uD83D\\uDDA5️ Mode Audio Syst\\xe8me (Exp\\xe9rimental)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Fonctionne uniquement sur Chrome/Edge (pas Firefox)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: '• Cliquez sur \"Capturer Audio Syst\\xe8me\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: '• S\\xe9lectionnez \"Partager l\\'audio syst\\xe8me\" dans la popup'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• L'audio de YouTube, Spotify, etc. sera trait\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• ⚠️ Peut causer des boucles audio - utilisez un casque\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Cas d'usage :\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Am\\xe9liorer l'audio de YouTube, Netflix, jeux, musique\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            activeMode === 'test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-600/10 border border-green-600/20 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-300 font-medium mb-2\",\n                                                children: \"\\uD83D\\uDD0A Mode Test\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Utilisez le g\\xe9n\\xe9rateur de sons int\\xe9gr\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Testez diff\\xe9rentes fr\\xe9quences (440Hz par d\\xe9faut)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Parfait pour calibrer et tester l'\\xe9galiseur\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Aucune source audio externe requise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Cas d'usage :\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Test et calibration de l'\\xe9galiseur, d\\xe9monstration\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            renderContent(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Informations Techniques\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white mb-2\",\n                                        children: \"Sp\\xe9cifications Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Fr\\xe9quences :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" 10 bandes (32Hz - 16kHz)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Plage EQ :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" -12dB \\xe0 +12dB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Volume :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" 0% \\xe0 200% (avec amplification)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Latence :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" ~10-20ms (Web Audio API)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Qualit\\xe9 :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" 44.1kHz, st\\xe9r\\xe9o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white mb-2\",\n                                        children: \"Compatibilit\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Chrome/Edge :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" ✅ Toutes fonctionnalit\\xe9s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Firefox :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" ⚠️ Microphone uniquement\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Safari :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" ⚠️ Support limit\\xe9\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Mobile :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \\uD83D\\uDCF1 Microphone uniquement\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"HTTPS requis :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Pour acc\\xe8s microphone\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"\\uD83D\\uDCA1 Conseils d'Utilisation\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white mb-2\",\n                                        children: \"Pour de meilleurs r\\xe9sultats :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Utilisez un casque pour \\xe9viter les boucles audio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Ajustez le volume d'entr\\xe9e avant l'\\xe9galiseur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Surveillez le niveau audio (\\xe9vitez la saturation)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Testez avec diff\\xe9rents types de musique\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white mb-2\",\n                                        children: \"D\\xe9pannage :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Pas de son → V\\xe9rifiez les permissions microphone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• \\xc9cho → Utilisez un casque ou baissez le volume\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Latence → Fermez les autres onglets audio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Qualit\\xe9 → V\\xe9rifiez la qualit\\xe9 du microphone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\CompleteEqualizer.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(CompleteEqualizer, \"C/a+rjEE+egpgMlGhoc9DcqjBp4=\");\n_c = CompleteEqualizer;\nvar _c;\n$RefreshReg$(_c, \"CompleteEqualizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CompleteEqualizer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/EqualizerNew.tsx":
/*!*****************************************!*\
  !*** ./src/components/EqualizerNew.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EqualizerNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioEngine */ \"(app-pages-browser)/./src/hooks/useAudioEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst PRESETS = {\n    flat: {\n        name: 'Plat',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    },\n    rock: {\n        name: 'Rock',\n        values: {\n            '32Hz': 4,\n            '64Hz': 3,\n            '125Hz': 1,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 3,\n            '8kHz': 4,\n            '16kHz': 3\n        }\n    },\n    pop: {\n        name: 'Pop',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 0,\n            '250Hz': 1,\n            '500Hz': 2,\n            '1kHz': 2,\n            '2kHz': 1,\n            '4kHz': 0,\n            '8kHz': 1,\n            '16kHz': 1\n        }\n    },\n    classical: {\n        name: 'Classique',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': -1,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 2\n        }\n    },\n    jazz: {\n        name: 'Jazz',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 1,\n            '250Hz': 2,\n            '500Hz': 1,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 1\n        }\n    },\n    electronic: {\n        name: 'Électronique',\n        values: {\n            '32Hz': 5,\n            '64Hz': 4,\n            '125Hz': 2,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 2,\n            '8kHz': 1,\n            '16kHz': 0\n        }\n    },\n    vocal: {\n        name: 'Vocal',\n        values: {\n            '32Hz': -2,\n            '64Hz': -1,\n            '125Hz': 0,\n            '250Hz': 3,\n            '500Hz': 4,\n            '1kHz': 4,\n            '2kHz': 3,\n            '4kHz': 1,\n            '8kHz': 0,\n            '16kHz': -1\n        }\n    }\n};\nfunction EqualizerNew() {\n    _s();\n    const [selectedPreset, setSelectedPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('flat');\n    const [isTestPlaying, setIsTestPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isInitialized, isPlaying, volume, frequencies, error, initializeAudio, setVolume, setEQFrequency, resetEQ, playTestTone } = (0,_hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__.useAudioEngine)();\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleVolumeChange]\": (e)=>{\n            const newVolume = parseInt(e.target.value);\n            setVolume(newVolume);\n        }\n    }[\"EqualizerNew.useCallback[handleVolumeChange]\"], [\n        setVolume\n    ]);\n    const handleFrequencyChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleFrequencyChange]\": (freq, value)=>{\n            setEQFrequency(freq, value);\n            setSelectedPreset('custom');\n        }\n    }[\"EqualizerNew.useCallback[handleFrequencyChange]\"], [\n        setEQFrequency\n    ]);\n    const handlePresetChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handlePresetChange]\": (presetKey)=>{\n            const preset = PRESETS[presetKey];\n            if (!preset) return;\n            setSelectedPreset(presetKey);\n            Object.entries(preset.values).forEach({\n                \"EqualizerNew.useCallback[handlePresetChange]\": (param)=>{\n                    let [freq, gain] = param;\n                    setEQFrequency(freq, gain);\n                }\n            }[\"EqualizerNew.useCallback[handlePresetChange]\"]);\n        }\n    }[\"EqualizerNew.useCallback[handlePresetChange]\"], [\n        setEQFrequency\n    ]);\n    const handleTestTone = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleTestTone]\": async ()=>{\n            setIsTestPlaying(true);\n            await playTestTone(440);\n            setTimeout({\n                \"EqualizerNew.useCallback[handleTestTone]\": ()=>setIsTestPlaying(false)\n            }[\"EqualizerNew.useCallback[handleTestTone]\"], 2000);\n        }\n    }[\"EqualizerNew.useCallback[handleTestTone]\"], [\n        playTestTone\n    ]);\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EqualizerNew.useCallback[handleReset]\": ()=>{\n            resetEQ();\n            setSelectedPreset('flat');\n        }\n    }[\"EqualizerNew.useCallback[handleReset]\"], [\n        resetEQ\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"\\xc9galiseur Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Contr\\xf4lez et personnalisez votre exp\\xe9rience audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 rounded-full \".concat(isInitialized ? 'bg-green-500' : 'bg-yellow-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: isInitialized ? 'Audio prêt' : 'Non initialisé'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            !isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: initializeAudio,\n                                className: \"px-4 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm\",\n                                children: \"Initialiser Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm\",\n                        children: [\n                            \"❌ \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Test Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleTestTone,\n                                disabled: !isInitialized || isPlaying || isTestPlaying,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-white font-medium\",\n                                children: isPlaying || isTestPlaying ? '🔊 Son en cours...' : '🔊 Test Son (440Hz)'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Cliquez pour jouer un son de test et tester l'\\xe9galiseur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Volume Principal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83D\\uDD0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"100\",\n                                        value: volume,\n                                        onChange: handleVolumeChange,\n                                        disabled: !isInitialized,\n                                        className: \"flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer disabled:cursor-not-allowed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium w-12\",\n                                        children: [\n                                            volume,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Volume actuel : \",\n                                    volume,\n                                    \"% \",\n                                    volume === 0 ? '(Muet)' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Pr\\xe9r\\xe9glages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: [\n                            Object.entries(PRESETS).map((param)=>{\n                                let [key, preset] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePresetChange(key),\n                                    disabled: !isInitialized,\n                                    className: \"p-3 rounded text-left transition-colors disabled:opacity-50 disabled:cursor-not-allowed \".concat(selectedPreset === key ? 'bg-blue-600/20 border border-blue-600/40 text-blue-300' : 'bg-gray-700 hover:bg-gray-600 text-white'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: preset.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                disabled: !isInitialized,\n                                className: \"p-3 rounded text-left bg-gray-700 hover:bg-gray-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-sm\",\n                                    children: \"\\uD83D\\uDD04 Reset\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-6\",\n                        children: \"\\xc9galiseur 10 Bandes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 md:grid-cols-10 gap-4\",\n                        children: Object.entries(frequencies).map((param)=>{\n                            let [freq, value] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-medium\",\n                                        children: freq\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 w-8 bg-gray-700 rounded relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"-12\",\n                                                max: \"12\",\n                                                value: value,\n                                                onChange: (e)=>handleFrequencyChange(freq, parseInt(e.target.value)),\n                                                disabled: !isInitialized,\n                                                className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed\",\n                                                style: {\n                                                    writingMode: 'bt-lr',\n                                                    WebkitAppearance: 'slider-vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 w-full rounded transition-all \".concat(value >= 0 ? 'bg-blue-500' : 'bg-red-500'),\n                                                style: {\n                                                    height: \"\".concat(50 + value * 2, \"%\"),\n                                                    minHeight: '4px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute w-6 h-2 bg-white rounded shadow-lg\",\n                                                style: {\n                                                    bottom: \"\".concat(50 + value * 2 - 4, \"%\"),\n                                                    left: '50%',\n                                                    transform: 'translateX(-50%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white font-medium\",\n                                        children: [\n                                            value > 0 ? '+' : '',\n                                            value,\n                                            \"dB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, freq, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Instructions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Initialiser :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Cliquez sur \"Initialiser Audio\" si pas encore fait'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Test :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Utilisez \"Test Son\" pour g\\xe9n\\xe9rer un son de 440Hz'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Volume :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Ajustez le volume principal avec le slider\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Presets :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Cliquez sur un preset pour appliquer des r\\xe9glages pr\\xe9d\\xe9finis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\xc9galiseur :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Bougez les sliders pour ajuster chaque bande de fr\\xe9quence\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reset :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Remet tous les r\\xe9glages \\xe0 z\\xe9ro\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\EqualizerNew.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(EqualizerNew, \"eVsgi+bECZLK/v3HT11nOeNZyoQ=\", false, function() {\n    return [\n        _hooks_useAudioEngine__WEBPACK_IMPORTED_MODULE_2__.useAudioEngine\n    ];\n});\n_c = EqualizerNew;\nvar _c;\n$RefreshReg$(_c, \"EqualizerNew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0VxdWFsaXplck5ldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU2QztBQUNVO0FBRXZELE1BQU1HLFVBQVU7SUFDZEMsTUFBTTtRQUFFQyxNQUFNO1FBQVFDLFFBQVE7WUFBRSxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsU0FBUztZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUTtZQUFHLFFBQVE7WUFBRyxTQUFTO1FBQUU7SUFBRTtJQUNuSkMsTUFBTTtRQUFFRixNQUFNO1FBQVFDLFFBQVE7WUFBRSxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsU0FBUyxDQUFDO1lBQUcsUUFBUTtZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7UUFBRTtJQUFFO0lBQ3BKRSxLQUFLO1FBQUVILE1BQU07UUFBT0MsUUFBUTtZQUFFLFFBQVE7WUFBRyxRQUFRO1lBQUcsU0FBUztZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsUUFBUTtZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7UUFBRTtJQUFFO0lBQ2pKRyxXQUFXO1FBQUVKLE1BQU07UUFBYUMsUUFBUTtZQUFFLFFBQVE7WUFBRyxRQUFRO1lBQUcsU0FBUztZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsUUFBUTtZQUFHLFFBQVEsQ0FBQztZQUFHLFFBQVEsQ0FBQztZQUFHLFFBQVE7WUFBRyxTQUFTO1FBQUU7SUFBRTtJQUMvSkksTUFBTTtRQUFFTCxNQUFNO1FBQVFDLFFBQVE7WUFBRSxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsU0FBUztZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUSxDQUFDO1lBQUcsUUFBUTtZQUFHLFNBQVM7UUFBRTtJQUFFO0lBQ3BKSyxZQUFZO1FBQUVOLE1BQU07UUFBZ0JDLFFBQVE7WUFBRSxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsU0FBUyxDQUFDO1lBQUcsUUFBUTtZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUTtZQUFHLFNBQVM7UUFBRTtJQUFFO0lBQ2xLTSxPQUFPO1FBQUVQLE1BQU07UUFBU0MsUUFBUTtZQUFFLFFBQVEsQ0FBQztZQUFHLFFBQVEsQ0FBQztZQUFHLFNBQVM7WUFBRyxTQUFTO1lBQUcsU0FBUztZQUFHLFFBQVE7WUFBRyxRQUFRO1lBQUcsUUFBUTtZQUFHLFFBQVE7WUFBRyxTQUFTLENBQUM7UUFBRTtJQUFFO0FBQzFKO0FBRWUsU0FBU087O0lBQ3RCLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2YsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDZ0IsZUFBZUMsaUJBQWlCLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNLEVBQ0prQixhQUFhLEVBQ2JDLFNBQVMsRUFDVEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLEtBQUssRUFDTEMsZUFBZSxFQUNmQyxTQUFTLEVBQ1RDLGNBQWMsRUFDZEMsT0FBTyxFQUNQQyxZQUFZLEVBQ2IsR0FBR3pCLHFFQUFjQTtJQUVsQixNQUFNMEIscUJBQXFCM0Isa0RBQVdBO3dEQUFDLENBQUM0QjtZQUN0QyxNQUFNQyxZQUFZQyxTQUFTRixFQUFFRyxNQUFNLENBQUNDLEtBQUs7WUFDekNULFVBQVVNO1FBQ1o7dURBQUc7UUFBQ047S0FBVTtJQUVkLE1BQU1VLHdCQUF3QmpDLGtEQUFXQTsyREFBQyxDQUFDa0MsTUFBY0Y7WUFDdkRSLGVBQWVVLE1BQU1GO1lBQ3JCbEIsa0JBQWtCO1FBQ3BCOzBEQUFHO1FBQUNVO0tBQWU7SUFFbkIsTUFBTVcscUJBQXFCbkMsa0RBQVdBO3dEQUFDLENBQUNvQztZQUN0QyxNQUFNQyxTQUFTbkMsT0FBTyxDQUFDa0MsVUFBa0M7WUFDekQsSUFBSSxDQUFDQyxRQUFRO1lBRWJ2QixrQkFBa0JzQjtZQUNsQkUsT0FBT0MsT0FBTyxDQUFDRixPQUFPaEMsTUFBTSxFQUFFbUMsT0FBTztnRUFBQzt3QkFBQyxDQUFDTixNQUFNTyxLQUFLO29CQUNqRGpCLGVBQWVVLE1BQU1PO2dCQUN2Qjs7UUFDRjt1REFBRztRQUFDakI7S0FBZTtJQUVuQixNQUFNa0IsaUJBQWlCMUMsa0RBQVdBO29EQUFDO1lBQ2pDZ0IsaUJBQWlCO1lBQ2pCLE1BQU1VLGFBQWE7WUFDbkJpQjs0REFBVyxJQUFNM0IsaUJBQWlCOzJEQUFRO1FBQzVDO21EQUFHO1FBQUNVO0tBQWE7SUFFakIsTUFBTWtCLGNBQWM1QyxrREFBV0E7aURBQUM7WUFDOUJ5QjtZQUNBWCxrQkFBa0I7UUFDcEI7Z0RBQUc7UUFBQ1c7S0FBUTtJQUVaLHFCQUNFLDhEQUFDb0I7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEOztrQ0FDQyw4REFBQ0U7d0JBQUdELFdBQVU7a0NBQXFDOzs7Ozs7a0NBQ25ELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBZ0I7Ozs7OztrQ0FHN0IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVcsaURBRWYsT0FEQzdCLGdCQUFnQixtQ0FBbUM7O2tEQUVuRCw4REFBQzRCO3dDQUFJQyxXQUFXLHdCQUF5RSxPQUFqRDdCLGdCQUFnQixpQkFBaUI7Ozs7OztrREFDekUsOERBQUNnQzt3Q0FBS0gsV0FBVTtrREFDYjdCLGdCQUFnQixlQUFlOzs7Ozs7Ozs7Ozs7NEJBSW5DLENBQUNBLCtCQUNBLDhEQUFDaUM7Z0NBQ0NDLFNBQVM3QjtnQ0FDVHdCLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7OztvQkFNSnpCLHVCQUNDLDhEQUFDd0I7d0JBQUlDLFdBQVU7OzRCQUErRTs0QkFDekZ6Qjs7Ozs7Ozs7Ozs7OzswQkFNVCw4REFBQ3dCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ007d0JBQUdOLFdBQVU7a0NBQXdDOzs7Ozs7a0NBQ3RELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNJO2dDQUNDQyxTQUFTVDtnQ0FDVFcsVUFBVSxDQUFDcEMsaUJBQWlCQyxhQUFhSDtnQ0FDekMrQixXQUFVOzBDQUVUNUIsYUFBYUgsZ0JBQWdCLHVCQUF1Qjs7Ozs7OzBDQUd2RCw4REFBQzhCO2dDQUFJQyxXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU8zQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FBS0gsV0FBVTtrREFBVzs7Ozs7O2tEQUMzQiw4REFBQ1E7d0NBQ0NDLE1BQUs7d0NBQ0xDLEtBQUk7d0NBQ0pDLEtBQUk7d0NBQ0p6QixPQUFPYjt3Q0FDUHVDLFVBQVUvQjt3Q0FDVjBCLFVBQVUsQ0FBQ3BDO3dDQUNYNkIsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDRzt3Q0FBS0gsV0FBVTs7NENBQStCM0I7NENBQU87Ozs7Ozs7Ozs7Ozs7MENBRXhELDhEQUFDMEI7Z0NBQUlDLFdBQVU7O29DQUF3QjtvQ0FDcEIzQjtvQ0FBTztvQ0FBR0EsV0FBVyxJQUFJLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTNELDhEQUFDMEI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVOzs0QkFDWlIsT0FBT0MsT0FBTyxDQUFDckMsU0FBU3lELEdBQUcsQ0FBQztvQ0FBQyxDQUFDQyxLQUFLdkIsT0FBTztxREFDekMsOERBQUNhO29DQUVDQyxTQUFTLElBQU1oQixtQkFBbUJ5QjtvQ0FDbENQLFVBQVUsQ0FBQ3BDO29DQUNYNkIsV0FBVywyRkFJVixPQUhDakMsbUJBQW1CK0MsTUFDZiwyREFDQTs4Q0FHTiw0RUFBQ2Y7d0NBQUlDLFdBQVU7a0RBQXVCVCxPQUFPakMsSUFBSTs7Ozs7O21DQVQ1Q3dEOzs7Ozs7MENBWVQsOERBQUNWO2dDQUNDQyxTQUFTUDtnQ0FDVFMsVUFBVSxDQUFDcEM7Z0NBQ1g2QixXQUFVOzBDQUVWLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0zQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaUixPQUFPQyxPQUFPLENBQUNuQixhQUFhdUMsR0FBRyxDQUFDO2dDQUFDLENBQUN6QixNQUFNRixNQUFNO2lEQUM3Qyw4REFBQ2E7Z0NBQWVDLFdBQVU7O2tEQUN4Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXFDWjs7Ozs7O2tEQUNwRCw4REFBQ1c7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDUTtnREFDQ0MsTUFBSztnREFDTEMsS0FBSTtnREFDSkMsS0FBSTtnREFDSnpCLE9BQU9BO2dEQUNQMEIsVUFBVSxDQUFDOUIsSUFBTUssc0JBQXNCQyxNQUFNSixTQUFTRixFQUFFRyxNQUFNLENBQUNDLEtBQUs7Z0RBQ3BFcUIsVUFBVSxDQUFDcEM7Z0RBQ1g2QixXQUFVO2dEQUNWZSxPQUFPO29EQUFFQyxhQUFhO29EQUFTQyxrQkFBa0I7Z0RBQWtCOzs7Ozs7MERBRXJFLDhEQUFDbEI7Z0RBQ0NDLFdBQVcsbURBRVYsT0FEQ2QsU0FBUyxJQUFJLGdCQUFnQjtnREFFL0I2QixPQUFPO29EQUNMRyxRQUFRLEdBQW9CLE9BQWpCLEtBQU1oQyxRQUFRLEdBQUc7b0RBQzVCaUMsV0FBVztnREFDYjs7Ozs7OzBEQUVGLDhEQUFDcEI7Z0RBQ0NDLFdBQVU7Z0RBQ1ZlLE9BQU87b0RBQ0xLLFFBQVEsR0FBd0IsT0FBckIsS0FBTWxDLFFBQVEsSUFBSyxHQUFFO29EQUNoQ21DLE1BQU07b0RBQ05DLFdBQVc7Z0RBQ2I7Ozs7Ozs7Ozs7OztrREFHSiw4REFBQ3ZCO3dDQUFJQyxXQUFVOzs0Q0FDWmQsUUFBUSxJQUFJLE1BQU07NENBQUlBOzRDQUFNOzs7Ozs7OzsrQkFoQ3ZCRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBd0NoQiw4REFBQ1c7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7O29DQUFFO2tEQUFFLDhEQUFDcUI7a0RBQU87Ozs7OztvQ0FBc0I7Ozs7Ozs7MENBQ25DLDhEQUFDckI7O29DQUFFO2tEQUFFLDhEQUFDcUI7a0RBQU87Ozs7OztvQ0FBZTs7Ozs7OzswQ0FDNUIsOERBQUNyQjs7b0NBQUU7a0RBQUUsOERBQUNxQjtrREFBTzs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FDOUIsOERBQUNyQjs7b0NBQUU7a0RBQUUsOERBQUNxQjtrREFBTzs7Ozs7O29DQUFrQjs7Ozs7OzswQ0FDL0IsOERBQUNyQjs7b0NBQUU7a0RBQUUsOERBQUNxQjtrREFBTzs7Ozs7O29DQUFvQjs7Ozs7OzswQ0FDakMsOERBQUNyQjs7b0NBQUU7a0RBQUUsOERBQUNxQjtrREFBTzs7Ozs7O29DQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt2QztHQWxOd0J6RDs7UUFlbEJYLGlFQUFjQTs7O0tBZklXIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsYSBLYWxib3Vzc2lcXERlc2t0b3BcXEV2ZXJ5XFxmcFxcZWdhbGlzZXVyXFxzcmNcXGNvbXBvbmVudHNcXEVxdWFsaXplck5ldy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlQXVkaW9FbmdpbmUgfSBmcm9tICdAL2hvb2tzL3VzZUF1ZGlvRW5naW5lJ1xuXG5jb25zdCBQUkVTRVRTID0ge1xuICBmbGF0OiB7IG5hbWU6ICdQbGF0JywgdmFsdWVzOiB7ICczMkh6JzogMCwgJzY0SHonOiAwLCAnMTI1SHonOiAwLCAnMjUwSHonOiAwLCAnNTAwSHonOiAwLCAnMWtIeic6IDAsICcya0h6JzogMCwgJzRrSHonOiAwLCAnOGtIeic6IDAsICcxNmtIeic6IDAgfSB9LFxuICByb2NrOiB7IG5hbWU6ICdSb2NrJywgdmFsdWVzOiB7ICczMkh6JzogNCwgJzY0SHonOiAzLCAnMTI1SHonOiAxLCAnMjUwSHonOiAwLCAnNTAwSHonOiAtMSwgJzFrSHonOiAwLCAnMmtIeic6IDEsICc0a0h6JzogMywgJzhrSHonOiA0LCAnMTZrSHonOiAzIH0gfSxcbiAgcG9wOiB7IG5hbWU6ICdQb3AnLCB2YWx1ZXM6IHsgJzMySHonOiAyLCAnNjRIeic6IDEsICcxMjVIeic6IDAsICcyNTBIeic6IDEsICc1MDBIeic6IDIsICcxa0h6JzogMiwgJzJrSHonOiAxLCAnNGtIeic6IDAsICc4a0h6JzogMSwgJzE2a0h6JzogMSB9IH0sXG4gIGNsYXNzaWNhbDogeyBuYW1lOiAnQ2xhc3NpcXVlJywgdmFsdWVzOiB7ICczMkh6JzogMCwgJzY0SHonOiAwLCAnMTI1SHonOiAwLCAnMjUwSHonOiAwLCAnNTAwSHonOiAwLCAnMWtIeic6IDAsICcya0h6JzogLTEsICc0a0h6JzogLTEsICc4a0h6JzogMCwgJzE2a0h6JzogMiB9IH0sXG4gIGpheno6IHsgbmFtZTogJ0phenonLCB2YWx1ZXM6IHsgJzMySHonOiAyLCAnNjRIeic6IDEsICcxMjVIeic6IDEsICcyNTBIeic6IDIsICc1MDBIeic6IDEsICcxa0h6JzogMCwgJzJrSHonOiAwLCAnNGtIeic6IC0xLCAnOGtIeic6IDAsICcxNmtIeic6IDEgfSB9LFxuICBlbGVjdHJvbmljOiB7IG5hbWU6ICfDiWxlY3Ryb25pcXVlJywgdmFsdWVzOiB7ICczMkh6JzogNSwgJzY0SHonOiA0LCAnMTI1SHonOiAyLCAnMjUwSHonOiAwLCAnNTAwSHonOiAtMSwgJzFrSHonOiAwLCAnMmtIeic6IDEsICc0a0h6JzogMiwgJzhrSHonOiAxLCAnMTZrSHonOiAwIH0gfSxcbiAgdm9jYWw6IHsgbmFtZTogJ1ZvY2FsJywgdmFsdWVzOiB7ICczMkh6JzogLTIsICc2NEh6JzogLTEsICcxMjVIeic6IDAsICcyNTBIeic6IDMsICc1MDBIeic6IDQsICcxa0h6JzogNCwgJzJrSHonOiAzLCAnNGtIeic6IDEsICc4a0h6JzogMCwgJzE2a0h6JzogLTEgfSB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVxdWFsaXplck5ldygpIHtcbiAgY29uc3QgW3NlbGVjdGVkUHJlc2V0LCBzZXRTZWxlY3RlZFByZXNldF0gPSB1c2VTdGF0ZSgnZmxhdCcpXG4gIGNvbnN0IFtpc1Rlc3RQbGF5aW5nLCBzZXRJc1Rlc3RQbGF5aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBcbiAgY29uc3Qge1xuICAgIGlzSW5pdGlhbGl6ZWQsXG4gICAgaXNQbGF5aW5nLFxuICAgIHZvbHVtZSxcbiAgICBmcmVxdWVuY2llcyxcbiAgICBlcnJvcixcbiAgICBpbml0aWFsaXplQXVkaW8sXG4gICAgc2V0Vm9sdW1lLFxuICAgIHNldEVRRnJlcXVlbmN5LFxuICAgIHJlc2V0RVEsXG4gICAgcGxheVRlc3RUb25lXG4gIH0gPSB1c2VBdWRpb0VuZ2luZSgpXG5cbiAgY29uc3QgaGFuZGxlVm9sdW1lQ2hhbmdlID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgbmV3Vm9sdW1lID0gcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpXG4gICAgc2V0Vm9sdW1lKG5ld1ZvbHVtZSlcbiAgfSwgW3NldFZvbHVtZV0pXG5cbiAgY29uc3QgaGFuZGxlRnJlcXVlbmN5Q2hhbmdlID0gdXNlQ2FsbGJhY2soKGZyZXE6IHN0cmluZywgdmFsdWU6IG51bWJlcikgPT4ge1xuICAgIHNldEVRRnJlcXVlbmN5KGZyZXEsIHZhbHVlKVxuICAgIHNldFNlbGVjdGVkUHJlc2V0KCdjdXN0b20nKVxuICB9LCBbc2V0RVFGcmVxdWVuY3ldKVxuXG4gIGNvbnN0IGhhbmRsZVByZXNldENoYW5nZSA9IHVzZUNhbGxiYWNrKChwcmVzZXRLZXk6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHByZXNldCA9IFBSRVNFVFNbcHJlc2V0S2V5IGFzIGtleW9mIHR5cGVvZiBQUkVTRVRTXVxuICAgIGlmICghcHJlc2V0KSByZXR1cm5cblxuICAgIHNldFNlbGVjdGVkUHJlc2V0KHByZXNldEtleSlcbiAgICBPYmplY3QuZW50cmllcyhwcmVzZXQudmFsdWVzKS5mb3JFYWNoKChbZnJlcSwgZ2Fpbl0pID0+IHtcbiAgICAgIHNldEVRRnJlcXVlbmN5KGZyZXEsIGdhaW4pXG4gICAgfSlcbiAgfSwgW3NldEVRRnJlcXVlbmN5XSlcblxuICBjb25zdCBoYW5kbGVUZXN0VG9uZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBzZXRJc1Rlc3RQbGF5aW5nKHRydWUpXG4gICAgYXdhaXQgcGxheVRlc3RUb25lKDQ0MClcbiAgICBzZXRUaW1lb3V0KCgpID0+IHNldElzVGVzdFBsYXlpbmcoZmFsc2UpLCAyMDAwKVxuICB9LCBbcGxheVRlc3RUb25lXSlcblxuICBjb25zdCBoYW5kbGVSZXNldCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICByZXNldEVRKClcbiAgICBzZXRTZWxlY3RlZFByZXNldCgnZmxhdCcpXG4gIH0sIFtyZXNldEVRXSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj7DiWdhbGlzZXVyIEF1ZGlvPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkNvbnRyw7RsZXogZXQgcGVyc29ubmFsaXNleiB2b3RyZSBleHDDqXJpZW5jZSBhdWRpbzwvcD5cbiAgICAgICAgXG4gICAgICAgIHsvKiBTdGF0dXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC0zIHB5LTEgcm91bmRlZCAke1xuICAgICAgICAgICAgaXNJbml0aWFsaXplZCA/ICdiZy1ncmVlbi02MDAvMjAgdGV4dC1ncmVlbi0zMDAnIDogJ2JnLXllbGxvdy02MDAvMjAgdGV4dC15ZWxsb3ctMzAwJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0yIGgtMiByb3VuZGVkLWZ1bGwgJHtpc0luaXRpYWxpemVkID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmcteWVsbG93LTUwMCd9YH0+PC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIHtpc0luaXRpYWxpemVkID8gJ0F1ZGlvIHByw6p0JyA6ICdOb24gaW5pdGlhbGlzw6knfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHshaXNJbml0aWFsaXplZCAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2luaXRpYWxpemVBdWRpb31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0xIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHJvdW5kZWQgdGV4dC13aGl0ZSB0ZXh0LXNtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgSW5pdGlhbGlzZXIgQXVkaW9cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHAtMyBiZy1yZWQtNjAwLzIwIGJvcmRlciBib3JkZXItcmVkLTYwMC80MCByb3VuZGVkIHRleHQtcmVkLTMwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICDinYwge2Vycm9yfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBUZXN0IEF1ZGlvICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+VGVzdCBBdWRpbzwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVGVzdFRvbmV9XG4gICAgICAgICAgICBkaXNhYmxlZD17IWlzSW5pdGlhbGl6ZWQgfHwgaXNQbGF5aW5nIHx8IGlzVGVzdFBsYXlpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTMgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6YmctZ3JheS02MDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHJvdW5kZWQgdGV4dC13aGl0ZSBmb250LW1lZGl1bVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzUGxheWluZyB8fCBpc1Rlc3RQbGF5aW5nID8gJ/CflIogU29uIGVuIGNvdXJzLi4uJyA6ICfwn5SKIFRlc3QgU29uICg0NDBIeiknfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICBDbGlxdWV6IHBvdXIgam91ZXIgdW4gc29uIGRlIHRlc3QgZXQgdGVzdGVyIGwnw6lnYWxpc2V1clxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVm9sdW1lIENvbnRyb2wgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5Wb2x1bWUgUHJpbmNpcGFsPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGxcIj7wn5SKPC9zcGFuPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICB2YWx1ZT17dm9sdW1lfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVm9sdW1lQ2hhbmdlfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWlzSW5pdGlhbGl6ZWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBoLTIgYmctZ3JheS03MDAgcm91bmRlZCBhcHBlYXJhbmNlLW5vbmUgY3Vyc29yLXBvaW50ZXIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHctMTJcIj57dm9sdW1lfSU8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgIFZvbHVtZSBhY3R1ZWwgOiB7dm9sdW1lfSUge3ZvbHVtZSA9PT0gMCA/ICcoTXVldCknIDogJyd9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcmVzZXRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+UHLDqXLDqWdsYWdlczwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtM1wiPlxuICAgICAgICAgIHtPYmplY3QuZW50cmllcyhQUkVTRVRTKS5tYXAoKFtrZXksIHByZXNldF0pID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtrZXl9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVByZXNldENoYW5nZShrZXkpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWlzSW5pdGlhbGl6ZWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMyByb3VuZGVkIHRleHQtbGVmdCB0cmFuc2l0aW9uLWNvbG9ycyBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCAke1xuICAgICAgICAgICAgICAgIHNlbGVjdGVkUHJlc2V0ID09PSBrZXlcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwLzIwIGJvcmRlciBib3JkZXItYmx1ZS02MDAvNDAgdGV4dC1ibHVlLTMwMCdcbiAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj57cHJlc2V0Lm5hbWV9PC9kaXY+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApKX1cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXNldH1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaXNJbml0aWFsaXplZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyByb3VuZGVkIHRleHQtbGVmdCBiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTYwMCB0ZXh0LXdoaXRlIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj7wn5SEIFJlc2V0PC9kaXY+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFcXVhbGl6ZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj7DiWdhbGlzZXVyIDEwIEJhbmRlczwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNSBtZDpncmlkLWNvbHMtMTAgZ2FwLTRcIj5cbiAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoZnJlcXVlbmNpZXMpLm1hcCgoW2ZyZXEsIHZhbHVlXSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2ZyZXF9IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBmb250LW1lZGl1bVwiPntmcmVxfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMzIgdy04IGJnLWdyYXktNzAwIHJvdW5kZWQgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICBtaW49XCItMTJcIlxuICAgICAgICAgICAgICAgICAgbWF4PVwiMTJcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3ZhbHVlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGcmVxdWVuY3lDaGFuZ2UoZnJlcSwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshaXNJbml0aWFsaXplZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbCBvcGFjaXR5LTAgY3Vyc29yLXBvaW50ZXIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdyaXRpbmdNb2RlOiAnYnQtbHInLCBXZWJraXRBcHBlYXJhbmNlOiAnc2xpZGVyLXZlcnRpY2FsJyB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIGJvdHRvbS0wIHctZnVsbCByb3VuZGVkIHRyYW5zaXRpb24tYWxsICR7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlID49IDAgPyAnYmctYmx1ZS01MDAnIDogJ2JnLXJlZC01MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IGAkezUwICsgKHZhbHVlICogMil9JWAsXG4gICAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzRweCdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdy02IGgtMiBiZy13aGl0ZSByb3VuZGVkIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgICAgICAgICAgYm90dG9tOiBgJHs1MCArICh2YWx1ZSAqIDIpIC0gNH0lYCxcbiAgICAgICAgICAgICAgICAgICAgbGVmdDogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVgoLTUwJSknXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgIHt2YWx1ZSA+IDAgPyAnKycgOiAnJ317dmFsdWV9ZEJcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEluc3RydWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPkluc3RydWN0aW9uczwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgIDxwPuKAoiA8c3Ryb25nPkluaXRpYWxpc2VyIDo8L3N0cm9uZz4gQ2xpcXVleiBzdXIgXCJJbml0aWFsaXNlciBBdWRpb1wiIHNpIHBhcyBlbmNvcmUgZmFpdDwvcD5cbiAgICAgICAgICA8cD7igKIgPHN0cm9uZz5UZXN0IDo8L3N0cm9uZz4gVXRpbGlzZXogXCJUZXN0IFNvblwiIHBvdXIgZ8OpbsOpcmVyIHVuIHNvbiBkZSA0NDBIejwvcD5cbiAgICAgICAgICA8cD7igKIgPHN0cm9uZz5Wb2x1bWUgOjwvc3Ryb25nPiBBanVzdGV6IGxlIHZvbHVtZSBwcmluY2lwYWwgYXZlYyBsZSBzbGlkZXI8L3A+XG4gICAgICAgICAgPHA+4oCiIDxzdHJvbmc+UHJlc2V0cyA6PC9zdHJvbmc+IENsaXF1ZXogc3VyIHVuIHByZXNldCBwb3VyIGFwcGxpcXVlciBkZXMgcsOpZ2xhZ2VzIHByw6lkw6lmaW5pczwvcD5cbiAgICAgICAgICA8cD7igKIgPHN0cm9uZz7DiWdhbGlzZXVyIDo8L3N0cm9uZz4gQm91Z2V6IGxlcyBzbGlkZXJzIHBvdXIgYWp1c3RlciBjaGFxdWUgYmFuZGUgZGUgZnLDqXF1ZW5jZTwvcD5cbiAgICAgICAgICA8cD7igKIgPHN0cm9uZz5SZXNldCA6PC9zdHJvbmc+IFJlbWV0IHRvdXMgbGVzIHLDqWdsYWdlcyDDoCB6w6lybzwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VBdWRpb0VuZ2luZSIsIlBSRVNFVFMiLCJmbGF0IiwibmFtZSIsInZhbHVlcyIsInJvY2siLCJwb3AiLCJjbGFzc2ljYWwiLCJqYXp6IiwiZWxlY3Ryb25pYyIsInZvY2FsIiwiRXF1YWxpemVyTmV3Iiwic2VsZWN0ZWRQcmVzZXQiLCJzZXRTZWxlY3RlZFByZXNldCIsImlzVGVzdFBsYXlpbmciLCJzZXRJc1Rlc3RQbGF5aW5nIiwiaXNJbml0aWFsaXplZCIsImlzUGxheWluZyIsInZvbHVtZSIsImZyZXF1ZW5jaWVzIiwiZXJyb3IiLCJpbml0aWFsaXplQXVkaW8iLCJzZXRWb2x1bWUiLCJzZXRFUUZyZXF1ZW5jeSIsInJlc2V0RVEiLCJwbGF5VGVzdFRvbmUiLCJoYW5kbGVWb2x1bWVDaGFuZ2UiLCJlIiwibmV3Vm9sdW1lIiwicGFyc2VJbnQiLCJ0YXJnZXQiLCJ2YWx1ZSIsImhhbmRsZUZyZXF1ZW5jeUNoYW5nZSIsImZyZXEiLCJoYW5kbGVQcmVzZXRDaGFuZ2UiLCJwcmVzZXRLZXkiLCJwcmVzZXQiLCJPYmplY3QiLCJlbnRyaWVzIiwiZm9yRWFjaCIsImdhaW4iLCJoYW5kbGVUZXN0VG9uZSIsInNldFRpbWVvdXQiLCJoYW5kbGVSZXNldCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJkaXNhYmxlZCIsImlucHV0IiwidHlwZSIsIm1pbiIsIm1heCIsIm9uQ2hhbmdlIiwibWFwIiwia2V5Iiwic3R5bGUiLCJ3cml0aW5nTW9kZSIsIldlYmtpdEFwcGVhcmFuY2UiLCJoZWlnaHQiLCJtaW5IZWlnaHQiLCJib3R0b20iLCJsZWZ0IiwidHJhbnNmb3JtIiwic3Ryb25nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EqualizerNew.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ScreenAudioCapture.tsx":
/*!***********************************************!*\
  !*** ./src/components/ScreenAudioCapture.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScreenAudioCapture)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScreenAudioCapture() {\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isCapturing: false,\n        isInitialized: false,\n        volume: 100,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        },\n        error: null,\n        audioLevel: 0\n    });\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const gainNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const analyserNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eqNodesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const outputAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const levelIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudio = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[initializeAudio]\": async ()=>{\n            try {\n                console.log('🎵 Initializing Screen Audio Capture...');\n                // Créer le contexte audio\n                audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n                if (audioContextRef.current.state === 'suspended') {\n                    await audioContextRef.current.resume();\n                }\n                // Créer les nœuds\n                gainNodeRef.current = audioContextRef.current.createGain();\n                analyserNodeRef.current = audioContextRef.current.createAnalyser();\n                // Créer les filtres d'égaliseur\n                eqNodesRef.current = eqFrequencies.map({\n                    \"ScreenAudioCapture.useCallback[initializeAudio]\": (freq, index)=>{\n                        const filter = audioContextRef.current.createBiquadFilter();\n                        if (index === 0) {\n                            filter.type = 'lowshelf';\n                        } else if (index === eqFrequencies.length - 1) {\n                            filter.type = 'highshelf';\n                        } else {\n                            filter.type = 'peaking';\n                        }\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"ScreenAudioCapture.useCallback[initializeAudio]\"]);\n                // Configurer l'analyseur\n                analyserNodeRef.current.fftSize = 256;\n                analyserNodeRef.current.smoothingTimeConstant = 0.8;\n                // Volume initial\n                gainNodeRef.current.gain.value = 1.0;\n                setState({\n                    \"ScreenAudioCapture.useCallback[initializeAudio]\": (prev)=>({\n                            ...prev,\n                            isInitialized: true,\n                            error: null\n                        })\n                }[\"ScreenAudioCapture.useCallback[initializeAudio]\"]);\n                console.log('✅ Screen Audio Capture initialized');\n            } catch (error) {\n                console.error('❌ Failed to initialize screen audio:', error);\n                setState({\n                    \"ScreenAudioCapture.useCallback[initializeAudio]\": (prev)=>({\n                            ...prev,\n                            error: error instanceof Error ? error.message : 'Initialization failed'\n                        })\n                }[\"ScreenAudioCapture.useCallback[initializeAudio]\"]);\n            }\n        }\n    }[\"ScreenAudioCapture.useCallback[initializeAudio]\"], []);\n    const connectAudioChain = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[connectAudioChain]\": ()=>{\n            if (!sourceNodeRef.current || !gainNodeRef.current || !analyserNodeRef.current) return;\n            // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination\n            let currentNode = sourceNodeRef.current;\n            // Volume\n            currentNode.connect(gainNodeRef.current);\n            currentNode = gainNodeRef.current;\n            // Égaliseur\n            eqNodesRef.current.forEach({\n                \"ScreenAudioCapture.useCallback[connectAudioChain]\": (eqNode)=>{\n                    currentNode.connect(eqNode);\n                    currentNode = eqNode;\n                }\n            }[\"ScreenAudioCapture.useCallback[connectAudioChain]\"]);\n            // Analyseur et sortie\n            currentNode.connect(analyserNodeRef.current);\n            currentNode.connect(audioContextRef.current.destination);\n            console.log('🔗 Screen audio chain connected');\n        }\n    }[\"ScreenAudioCapture.useCallback[connectAudioChain]\"], []);\n    const startScreenCapture = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[startScreenCapture]\": async ()=>{\n            if (!state.isInitialized) {\n                await initializeAudio();\n            }\n            try {\n                console.log('🖥️ Starting screen audio capture...');\n                // Demander la capture d'écran avec audio\n                const stream = await navigator.mediaDevices.getDisplayMedia({\n                    video: false,\n                    audio: {\n                        echoCancellation: false,\n                        noiseSuppression: false,\n                        autoGainControl: false,\n                        sampleRate: 44100\n                    }\n                });\n                streamRef.current = stream;\n                // Créer le source node\n                sourceNodeRef.current = audioContextRef.current.createMediaStreamSource(stream);\n                // Connecter la chaîne audio\n                connectAudioChain();\n                // Démarrer le monitoring du niveau\n                levelIntervalRef.current = setInterval({\n                    \"ScreenAudioCapture.useCallback[startScreenCapture]\": ()=>{\n                        if (analyserNodeRef.current) {\n                            const bufferLength = analyserNodeRef.current.frequencyBinCount;\n                            const dataArray = new Uint8Array(bufferLength);\n                            analyserNodeRef.current.getByteFrequencyData(dataArray);\n                            let sum = 0;\n                            for(let i = 0; i < bufferLength; i++){\n                                sum += dataArray[i];\n                            }\n                            const level = sum / bufferLength / 255 * 100;\n                            setState({\n                                \"ScreenAudioCapture.useCallback[startScreenCapture]\": (prev)=>({\n                                        ...prev,\n                                        audioLevel: level\n                                    })\n                            }[\"ScreenAudioCapture.useCallback[startScreenCapture]\"]);\n                        }\n                    }\n                }[\"ScreenAudioCapture.useCallback[startScreenCapture]\"], 100);\n                setState({\n                    \"ScreenAudioCapture.useCallback[startScreenCapture]\": (prev)=>({\n                            ...prev,\n                            isCapturing: true,\n                            error: null\n                        })\n                }[\"ScreenAudioCapture.useCallback[startScreenCapture]\"]);\n                console.log('✅ Screen audio capture started');\n            } catch (error) {\n                console.error('❌ Error starting screen capture:', error);\n                setState({\n                    \"ScreenAudioCapture.useCallback[startScreenCapture]\": (prev)=>({\n                            ...prev,\n                            error: error instanceof Error ? error.message : 'Failed to start screen capture'\n                        })\n                }[\"ScreenAudioCapture.useCallback[startScreenCapture]\"]);\n            }\n        }\n    }[\"ScreenAudioCapture.useCallback[startScreenCapture]\"], [\n        state.isInitialized,\n        initializeAudio,\n        connectAudioChain\n    ]);\n    const stopCapture = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[stopCapture]\": ()=>{\n            console.log('⏹️ Stopping screen audio capture...');\n            if (sourceNodeRef.current) {\n                sourceNodeRef.current.disconnect();\n                sourceNodeRef.current = null;\n            }\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach({\n                    \"ScreenAudioCapture.useCallback[stopCapture]\": (track)=>track.stop()\n                }[\"ScreenAudioCapture.useCallback[stopCapture]\"]);\n                streamRef.current = null;\n            }\n            if (levelIntervalRef.current) {\n                clearInterval(levelIntervalRef.current);\n                levelIntervalRef.current = null;\n            }\n            setState({\n                \"ScreenAudioCapture.useCallback[stopCapture]\": (prev)=>({\n                        ...prev,\n                        isCapturing: false,\n                        audioLevel: 0\n                    })\n            }[\"ScreenAudioCapture.useCallback[stopCapture]\"]);\n            console.log('✅ Screen audio capture stopped');\n        }\n    }[\"ScreenAudioCapture.useCallback[stopCapture]\"], []);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[setVolume]\": (volume)=>{\n            if (!gainNodeRef.current || !audioContextRef.current) return;\n            const clampedVolume = Math.max(0, Math.min(200, volume));\n            const gainValue = clampedVolume / 100;\n            gainNodeRef.current.gain.setValueAtTime(gainValue, audioContextRef.current.currentTime);\n            setState({\n                \"ScreenAudioCapture.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"ScreenAudioCapture.useCallback[setVolume]\"]);\n            console.log(\"\\uD83D\\uDD0A Screen volume set to: \".concat(clampedVolume, \"%\"));\n        }\n    }[\"ScreenAudioCapture.useCallback[setVolume]\"], []);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            if (!audioContextRef.current) return;\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !eqNodesRef.current[freqIndex]) return;\n            const clampedGain = Math.max(-12, Math.min(12, gain));\n            eqNodesRef.current[freqIndex].gain.setValueAtTime(clampedGain, audioContextRef.current.currentTime);\n            setState({\n                \"ScreenAudioCapture.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"ScreenAudioCapture.useCallback[setEQFrequency]\"]);\n            console.log(\"\\uD83C\\uDF9B️ Screen EQ \".concat(freqKey, \" set to: \").concat(clampedGain, \"dB\"));\n        }\n    }[\"ScreenAudioCapture.useCallback[setEQFrequency]\"], [\n        state.frequencies\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScreenAudioCapture.useCallback[resetEQ]\": ()=>{\n            if (!audioContextRef.current) return;\n            eqNodesRef.current.forEach({\n                \"ScreenAudioCapture.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, audioContextRef.current.currentTime);\n                }\n            }[\"ScreenAudioCapture.useCallback[resetEQ]\"]);\n            setState({\n                \"ScreenAudioCapture.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"ScreenAudioCapture.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"ScreenAudioCapture.useCallback[resetEQ]\"], {})\n                    })\n            }[\"ScreenAudioCapture.useCallback[resetEQ]\"]);\n            console.log('🔄 Screen EQ reset');\n        }\n    }[\"ScreenAudioCapture.useCallback[resetEQ]\"], []);\n    // Cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScreenAudioCapture.useEffect\": ()=>{\n            return ({\n                \"ScreenAudioCapture.useEffect\": ()=>{\n                    stopCapture();\n                    if (audioContextRef.current) {\n                        audioContextRef.current.close();\n                    }\n                }\n            })[\"ScreenAudioCapture.useEffect\"];\n        }\n    }[\"ScreenAudioCapture.useEffect\"], [\n        stopCapture\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-white mb-4\",\n                children: \"Capture Audio Syst\\xe8me (Exp\\xe9rimental)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(state.isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(state.isInitialized ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: state.isInitialized ? 'Prêt' : 'Non initialisé'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(state.isCapturing ? 'bg-blue-600/20 text-blue-300' : 'bg-gray-600/20 text-gray-300'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(state.isCapturing ? 'bg-blue-500' : 'bg-gray-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: state.isCapturing ? 'Capture active' : 'Arrêté'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm\",\n                children: [\n                    \"❌ \",\n                    state.error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: state.isCapturing ? stopCapture : startScreenCapture,\n                                className: \"px-6 py-3 rounded font-medium \".concat(state.isCapturing ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                children: state.isCapturing ? '⏹️ Arrêter' : '🖥️ Capturer Audio Système'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            !state.isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: initializeAudio,\n                                className: \"px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white\",\n                                children: \"Initialiser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    state.isCapturing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Niveau audio:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-700 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full rounded-full transition-all duration-100 \".concat(state.audioLevel > 80 ? 'bg-red-500' : state.audioLevel > 50 ? 'bg-yellow-500' : 'bg-green-500'),\n                                    style: {\n                                        width: \"\".concat(state.audioLevel, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-white w-12\",\n                                children: [\n                                    Math.round(state.audioLevel),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    state.isCapturing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm text-gray-300\",\n                                children: \"Volume de sortie\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"\\uD83D\\uDD0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"200\",\n                                        value: state.volume,\n                                        onChange: (e)=>setVolume(parseInt(e.target.value)),\n                                        className: \"flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium w-16\",\n                                        children: [\n                                            state.volume,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-gray-700/50 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-300 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"⚠️ Fonctionnalit\\xe9 exp\\xe9rimentale\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-xs text-gray-400 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• Cliquez sur \"Capturer Audio Syst\\xe8me\" pour commencer'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• S\\xe9lectionnez \"Partager l\\'audio syst\\xe8me\" dans la popup'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• L'audio de votre syst\\xe8me sera trait\\xe9 par l'\\xe9galiseur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Fonctionne avec Chrome/Edge (pas Firefox)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\ScreenAudioCapture.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, this);\n}\n_s(ScreenAudioCapture, \"15utYZu8ZsHprQwbujTflT1cUfs=\");\n_c = ScreenAudioCapture;\nvar _c;\n$RefreshReg$(_c, \"ScreenAudioCapture\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScreenAudioCapture.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SystemEqualizer.tsx":
/*!********************************************!*\
  !*** ./src/components/SystemEqualizer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemEqualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSystemAudio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSystemAudio */ \"(app-pages-browser)/./src/hooks/useSystemAudio.ts\");\n/* harmony import */ var _AudioVisualizerNew__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AudioVisualizerNew */ \"(app-pages-browser)/./src/components/AudioVisualizerNew.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PRESETS = {\n    flat: {\n        name: 'Plat',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    },\n    rock: {\n        name: 'Rock',\n        values: {\n            '32Hz': 4,\n            '64Hz': 3,\n            '125Hz': 1,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 3,\n            '8kHz': 4,\n            '16kHz': 3\n        }\n    },\n    pop: {\n        name: 'Pop',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 0,\n            '250Hz': 1,\n            '500Hz': 2,\n            '1kHz': 2,\n            '2kHz': 1,\n            '4kHz': 0,\n            '8kHz': 1,\n            '16kHz': 1\n        }\n    },\n    classical: {\n        name: 'Classique',\n        values: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': -1,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 2\n        }\n    },\n    jazz: {\n        name: 'Jazz',\n        values: {\n            '32Hz': 2,\n            '64Hz': 1,\n            '125Hz': 1,\n            '250Hz': 2,\n            '500Hz': 1,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': -1,\n            '8kHz': 0,\n            '16kHz': 1\n        }\n    },\n    electronic: {\n        name: 'Électronique',\n        values: {\n            '32Hz': 5,\n            '64Hz': 4,\n            '125Hz': 2,\n            '250Hz': 0,\n            '500Hz': -1,\n            '1kHz': 0,\n            '2kHz': 1,\n            '4kHz': 2,\n            '8kHz': 1,\n            '16kHz': 0\n        }\n    },\n    vocal: {\n        name: 'Vocal',\n        values: {\n            '32Hz': -2,\n            '64Hz': -1,\n            '125Hz': 0,\n            '250Hz': 3,\n            '500Hz': 4,\n            '1kHz': 4,\n            '2kHz': 3,\n            '4kHz': 1,\n            '8kHz': 0,\n            '16kHz': -1\n        }\n    }\n};\nfunction SystemEqualizer() {\n    _s();\n    const [selectedPreset, setSelectedPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('flat');\n    const outputAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { isCapturing, isInitialized, volume, frequencies, error, inputDevices, selectedInputDevice, audioLevel, outputStream, initializeAudio, startCapture, stopCapture, setVolume, setEQFrequency, resetEQ, getAnalyserData, setInputDevice } = (0,_hooks_useSystemAudio__WEBPACK_IMPORTED_MODULE_2__.useSystemAudio)();\n    // Connecter le stream de sortie à l'élément audio pour la lecture\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemEqualizer.useEffect\": ()=>{\n            if (outputStream && outputAudioRef.current) {\n                outputAudioRef.current.srcObject = outputStream;\n                outputAudioRef.current.play().catch(console.error);\n            }\n        }\n    }[\"SystemEqualizer.useEffect\"], [\n        outputStream\n    ]);\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SystemEqualizer.useCallback[handleVolumeChange]\": (e)=>{\n            const newVolume = parseInt(e.target.value);\n            setVolume(newVolume);\n        }\n    }[\"SystemEqualizer.useCallback[handleVolumeChange]\"], [\n        setVolume\n    ]);\n    const handleFrequencyChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SystemEqualizer.useCallback[handleFrequencyChange]\": (freq, value)=>{\n            setEQFrequency(freq, value);\n            setSelectedPreset('custom');\n        }\n    }[\"SystemEqualizer.useCallback[handleFrequencyChange]\"], [\n        setEQFrequency\n    ]);\n    const handlePresetChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SystemEqualizer.useCallback[handlePresetChange]\": (presetKey)=>{\n            const preset = PRESETS[presetKey];\n            if (!preset) return;\n            setSelectedPreset(presetKey);\n            Object.entries(preset.values).forEach({\n                \"SystemEqualizer.useCallback[handlePresetChange]\": (param)=>{\n                    let [freq, gain] = param;\n                    setEQFrequency(freq, gain);\n                }\n            }[\"SystemEqualizer.useCallback[handlePresetChange]\"]);\n        }\n    }[\"SystemEqualizer.useCallback[handlePresetChange]\"], [\n        setEQFrequency\n    ]);\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SystemEqualizer.useCallback[handleReset]\": ()=>{\n            resetEQ();\n            setSelectedPreset('flat');\n        }\n    }[\"SystemEqualizer.useCallback[handleReset]\"], [\n        resetEQ\n    ]);\n    const handleDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SystemEqualizer.useCallback[handleDeviceChange]\": (e)=>{\n            setInputDevice(e.target.value);\n        }\n    }[\"SystemEqualizer.useCallback[handleDeviceChange]\"], [\n        setInputDevice\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"\\xc9galiseur Audio Syst\\xe8me\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Traitement audio en temps r\\xe9el de votre syst\\xe8me\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(isInitialized ? 'bg-green-600/20 text-green-300' : 'bg-yellow-600/20 text-yellow-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 rounded-full \".concat(isInitialized ? 'bg-green-500' : 'bg-yellow-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: isInitialized ? 'Audio prêt' : 'Non initialisé'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 px-3 py-1 rounded \".concat(isCapturing ? 'bg-blue-600/20 text-blue-300' : 'bg-gray-600/20 text-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 rounded-full \".concat(isCapturing ? 'bg-blue-500' : 'bg-gray-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: isCapturing ? 'Capture active' : 'Capture arrêtée'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            !isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: initializeAudio,\n                                className: \"px-4 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm\",\n                                children: \"Initialiser Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 p-3 bg-red-600/20 border border-red-600/40 rounded text-red-300 text-sm\",\n                        children: [\n                            \"❌ \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Source Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                        children: \"P\\xe9riph\\xe9rique d'entr\\xe9e\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedInputDevice,\n                                        onChange: handleDeviceChange,\n                                        disabled: isCapturing,\n                                        className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white disabled:opacity-50\",\n                                        children: inputDevices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: device.deviceId,\n                                                children: device.label || \"Microphone \".concat(device.deviceId.slice(0, 8))\n                                            }, device.deviceId, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: isCapturing ? stopCapture : startCapture,\n                                        disabled: !isInitialized,\n                                        className: \"px-6 py-3 rounded font-medium disabled:opacity-50 disabled:cursor-not-allowed \".concat(isCapturing ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'),\n                                        children: isCapturing ? '⏹️ Arrêter Capture' : '🎤 Démarrer Capture'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Niveau:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full rounded-full transition-all duration-100 \".concat(audioLevel > 80 ? 'bg-red-500' : audioLevel > 50 ? 'bg-yellow-500' : 'bg-green-500'),\n                                                        style: {\n                                                            width: \"\".concat(audioLevel, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white w-12\",\n                                                    children: [\n                                                        Math.round(audioLevel),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Volume Principal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83D\\uDD0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"200\",\n                                        value: volume,\n                                        onChange: handleVolumeChange,\n                                        disabled: !isInitialized,\n                                        className: \"flex-1 h-2 bg-gray-700 rounded appearance-none cursor-pointer disabled:cursor-not-allowed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium w-16\",\n                                        children: [\n                                            volume,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Volume syst\\xe8me : \",\n                                    volume,\n                                    \"% \",\n                                    volume === 0 ? '(Muet)' : volume > 100 ? '(Amplifié)' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Pr\\xe9r\\xe9glages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: [\n                            Object.entries(PRESETS).map((param)=>{\n                                let [key, preset] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePresetChange(key),\n                                    disabled: !isInitialized,\n                                    className: \"p-3 rounded text-left transition-colors disabled:opacity-50 disabled:cursor-not-allowed \".concat(selectedPreset === key ? 'bg-blue-600/20 border border-blue-600/40 text-blue-300' : 'bg-gray-700 hover:bg-gray-600 text-white'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: preset.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                disabled: !isInitialized,\n                                className: \"p-3 rounded text-left bg-gray-700 hover:bg-gray-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-sm\",\n                                    children: \"\\uD83D\\uDD04 Reset\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-6\",\n                        children: \"\\xc9galiseur 10 Bandes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 md:grid-cols-10 gap-4\",\n                        children: Object.entries(frequencies).map((param)=>{\n                            let [freq, value] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-medium\",\n                                        children: freq\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 w-8 bg-gray-700 rounded relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"-12\",\n                                                max: \"12\",\n                                                value: value,\n                                                onChange: (e)=>handleFrequencyChange(freq, parseInt(e.target.value)),\n                                                disabled: !isInitialized,\n                                                className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed\",\n                                                style: {\n                                                    writingMode: 'bt-lr',\n                                                    WebkitAppearance: 'slider-vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 w-full rounded transition-all \".concat(value >= 0 ? 'bg-blue-500' : 'bg-red-500'),\n                                                style: {\n                                                    height: \"\".concat(50 + value * 2, \"%\"),\n                                                    minHeight: '4px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute w-6 h-2 bg-white rounded shadow-lg\",\n                                                style: {\n                                                    bottom: \"\".concat(50 + value * 2 - 4, \"%\"),\n                                                    left: '50%',\n                                                    transform: 'translateX(-50%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white font-medium\",\n                                        children: [\n                                            value > 0 ? '+' : '',\n                                            value,\n                                            \"dB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, freq, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AudioVisualizerNew__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isActive: isCapturing,\n                getAnalyserData: getAnalyserData,\n                title: \"Visualisation Audio Syst\\xe8me\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Instructions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Initialiser :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Cliquez sur \"Initialiser Audio\" pour pr\\xe9parer le syst\\xe8me'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"P\\xe9riph\\xe9rique :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" S\\xe9lectionnez votre source audio (microphone, ligne d'entr\\xe9e)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Capture :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 16\n                                    }, this),\n                                    ' Cliquez sur \"D\\xe9marrer Capture\" pour commencer le traitement'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Volume :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Ajustez le volume de sortie (0-200% avec amplification)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\xc9galiseur :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Modifiez les fr\\xe9quences en temps r\\xe9el\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Niveau :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Surveillez le niveau d'entr\\xe9e pour \\xe9viter la saturation\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: outputAudioRef,\n                autoPlay: true,\n                muted: false,\n                style: {\n                    display: 'none'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\SystemEqualizer.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemEqualizer, \"KAYkEHLke1x0AbKUm7ZWFxqsE9Y=\", false, function() {\n    return [\n        _hooks_useSystemAudio__WEBPACK_IMPORTED_MODULE_2__.useSystemAudio\n    ];\n});\n_c = SystemEqualizer;\nvar _c;\n$RefreshReg$(_c, \"SystemEqualizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SystemEqualizer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useSystemAudio.ts":
/*!*************************************!*\
  !*** ./src/hooks/useSystemAudio.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSystemAudio: () => (/* binding */ useSystemAudio)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useSystemAudio auto */ \nclass SystemAudioEngine {\n    async initialize() {\n        try {\n            console.log('🎵 Initializing System Audio Engine...');\n            // Créer le contexte audio\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n            // Créer les nœuds audio\n            this.gainNode = this.audioContext.createGain();\n            this.analyserNode = this.audioContext.createAnalyser();\n            this.destinationNode = this.audioContext.createMediaStreamDestination();\n            // Créer les filtres d'égaliseur\n            this.eqNodes = this.eqFrequencies.map((freq, index)=>{\n                const filter = this.audioContext.createBiquadFilter();\n                if (index === 0) {\n                    filter.type = 'lowshelf';\n                } else if (index === this.eqFrequencies.length - 1) {\n                    filter.type = 'highshelf';\n                } else {\n                    filter.type = 'peaking';\n                }\n                filter.frequency.value = freq;\n                filter.Q.value = 1;\n                filter.gain.value = 0;\n                return filter;\n            });\n            // Configurer l'analyseur\n            this.analyserNode.fftSize = 256;\n            this.analyserNode.smoothingTimeConstant = 0.8;\n            // Volume initial\n            this.gainNode.gain.value = 1.0;\n            console.log('✅ System Audio Engine initialized');\n            return true;\n        } catch (error) {\n            console.error('❌ Failed to initialize System Audio Engine:', error);\n            return false;\n        }\n    }\n    connectAudioChain() {\n        if (!this.sourceNode || !this.gainNode || !this.analyserNode || !this.destinationNode) return;\n        // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination\n        let currentNode = this.sourceNode;\n        // Volume\n        currentNode.connect(this.gainNode);\n        currentNode = this.gainNode;\n        // Égaliseur\n        this.eqNodes.forEach((eqNode)=>{\n            currentNode.connect(eqNode);\n            currentNode = eqNode;\n        });\n        // Analyseur et sortie\n        currentNode.connect(this.analyserNode);\n        currentNode.connect(this.destinationNode);\n        console.log('🔗 Audio chain connected');\n    }\n    async startCapture(deviceId) {\n        if (!this.audioContext) {\n            throw new Error('Audio engine not initialized');\n        }\n        try {\n            var _this_destinationNode;\n            console.log('🎤 Starting audio capture...');\n            // Configuration de capture\n            const constraints = {\n                audio: {\n                    deviceId: deviceId ? {\n                        exact: deviceId\n                    } : undefined,\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: false,\n                    sampleRate: 44100,\n                    channelCount: 2\n                },\n                video: false\n            };\n            // Demander l'accès au microphone/audio système\n            this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);\n            // Créer le source node\n            this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream);\n            // Connecter la chaîne audio\n            this.connectAudioChain();\n            console.log('✅ Audio capture started');\n            // Retourner le stream de sortie traité\n            return ((_this_destinationNode = this.destinationNode) === null || _this_destinationNode === void 0 ? void 0 : _this_destinationNode.stream) || null;\n        } catch (error) {\n            console.error('❌ Error starting audio capture:', error);\n            throw error;\n        }\n    }\n    stopCapture() {\n        console.log('⏹️ Stopping audio capture...');\n        if (this.sourceNode) {\n            this.sourceNode.disconnect();\n            this.sourceNode = null;\n        }\n        if (this.mediaStream) {\n            this.mediaStream.getTracks().forEach((track)=>track.stop());\n            this.mediaStream = null;\n        }\n        console.log('✅ Audio capture stopped');\n    }\n    setVolume(volume) {\n        if (!this.gainNode || !this.audioContext) return;\n        const clampedVolume = Math.max(0, Math.min(200, volume)) // 0-200% pour amplification\n        ;\n        const gainValue = clampedVolume / 100;\n        this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);\n        console.log(\"\\uD83D\\uDD0A System volume set to: \".concat(clampedVolume, \"% (gain: \").concat(gainValue, \")\"));\n    }\n    setEQBand(bandIndex, gain) {\n        if (!this.eqNodes[bandIndex] || !this.audioContext) return;\n        const clampedGain = Math.max(-12, Math.min(12, gain));\n        this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime);\n        const freq = this.eqFrequencies[bandIndex];\n        console.log(\"\\uD83C\\uDF9B️ System EQ \".concat(freq, \"Hz set to: \").concat(clampedGain, \"dB\"));\n    }\n    resetEQ() {\n        this.eqNodes.forEach((node)=>{\n            if (this.audioContext) {\n                node.gain.setValueAtTime(0, this.audioContext.currentTime);\n            }\n        });\n        console.log('🔄 System EQ reset to flat');\n    }\n    getAnalyserData() {\n        if (!this.analyserNode) return new Uint8Array(0);\n        const bufferLength = this.analyserNode.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        this.analyserNode.getByteFrequencyData(dataArray);\n        return dataArray;\n    }\n    getAudioLevel() {\n        if (!this.analyserNode) return 0;\n        const bufferLength = this.analyserNode.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        this.analyserNode.getByteFrequencyData(dataArray);\n        // Calculer le niveau moyen\n        let sum = 0;\n        for(let i = 0; i < bufferLength; i++){\n            sum += dataArray[i];\n        }\n        return sum / bufferLength / 255 * 100;\n    }\n    async getInputDevices() {\n        try {\n            const devices = await navigator.mediaDevices.enumerateDevices();\n            return devices.filter((device)=>device.kind === 'audioinput');\n        } catch (error) {\n            console.error('❌ Error getting input devices:', error);\n            return [];\n        }\n    }\n    destroy() {\n        this.stopCapture();\n        if (this.audioContext) {\n            this.audioContext.close();\n        }\n        console.log('🗑️ System Audio Engine destroyed');\n    }\n    constructor(){\n        this.audioContext = null;\n        this.gainNode = null;\n        this.analyserNode = null;\n        this.eqNodes = [];\n        this.sourceNode = null;\n        this.mediaStream = null;\n        this.destinationNode = null;\n        this.eqFrequencies = [\n            32,\n            64,\n            125,\n            250,\n            500,\n            1000,\n            2000,\n            4000,\n            8000,\n            16000\n        ];\n    }\n}\nconst useSystemAudio = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCapturing: false,\n        isInitialized: false,\n        volume: 100,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        },\n        error: null,\n        inputDevices: [],\n        selectedInputDevice: '',\n        audioLevel: 0\n    });\n    const audioEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const outputStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const levelIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const initializeAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[initializeAudio]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                if (!audioEngineRef.current) {\n                    audioEngineRef.current = new SystemAudioEngine();\n                }\n                const success = await audioEngineRef.current.initialize();\n                if (success) {\n                    // Récupérer les périphériques d'entrée\n                    const devices = await audioEngineRef.current.getInputDevices();\n                    setState({\n                        \"useSystemAudio.useCallback[initializeAudio]\": (prev)=>{\n                            var _devices_;\n                            return {\n                                ...prev,\n                                isInitialized: success,\n                                inputDevices: devices,\n                                selectedInputDevice: ((_devices_ = devices[0]) === null || _devices_ === void 0 ? void 0 : _devices_.deviceId) || '',\n                                error: null\n                            };\n                        }\n                    }[\"useSystemAudio.useCallback[initializeAudio]\"]);\n                } else {\n                    setState({\n                        \"useSystemAudio.useCallback[initializeAudio]\": (prev)=>({\n                                ...prev,\n                                error: 'Failed to initialize system audio'\n                            })\n                    }[\"useSystemAudio.useCallback[initializeAudio]\"]);\n                }\n            } catch (error) {\n                console.error('❌ Failed to initialize system audio:', error);\n                setState({\n                    \"useSystemAudio.useCallback[initializeAudio]\": (prev)=>({\n                            ...prev,\n                            error: error instanceof Error ? error.message : 'Unknown error'\n                        })\n                }[\"useSystemAudio.useCallback[initializeAudio]\"]);\n            }\n        }\n    }[\"useSystemAudio.useCallback[initializeAudio]\"], [\n        state.isInitialized\n    ]);\n    const startCapture = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[startCapture]\": async ()=>{\n            if (!audioEngineRef.current || !state.isInitialized) {\n                await initializeAudio();\n                if (!audioEngineRef.current) return;\n            }\n            try {\n                setState({\n                    \"useSystemAudio.useCallback[startCapture]\": (prev)=>({\n                            ...prev,\n                            error: null\n                        })\n                }[\"useSystemAudio.useCallback[startCapture]\"]);\n                const outputStream = await audioEngineRef.current.startCapture(state.selectedInputDevice);\n                outputStreamRef.current = outputStream;\n                // Démarrer le monitoring du niveau audio\n                levelIntervalRef.current = setInterval({\n                    \"useSystemAudio.useCallback[startCapture]\": ()=>{\n                        if (audioEngineRef.current) {\n                            const level = audioEngineRef.current.getAudioLevel();\n                            setState({\n                                \"useSystemAudio.useCallback[startCapture]\": (prev)=>({\n                                        ...prev,\n                                        audioLevel: level\n                                    })\n                            }[\"useSystemAudio.useCallback[startCapture]\"]);\n                        }\n                    }\n                }[\"useSystemAudio.useCallback[startCapture]\"], 100);\n                setState({\n                    \"useSystemAudio.useCallback[startCapture]\": (prev)=>({\n                            ...prev,\n                            isCapturing: true\n                        })\n                }[\"useSystemAudio.useCallback[startCapture]\"]);\n                console.log('🎵 System audio capture started');\n            } catch (error) {\n                console.error('❌ Error starting capture:', error);\n                setState({\n                    \"useSystemAudio.useCallback[startCapture]\": (prev)=>({\n                            ...prev,\n                            error: error instanceof Error ? error.message : 'Failed to start capture'\n                        })\n                }[\"useSystemAudio.useCallback[startCapture]\"]);\n            }\n        }\n    }[\"useSystemAudio.useCallback[startCapture]\"], [\n        state.isInitialized,\n        state.selectedInputDevice,\n        initializeAudio\n    ]);\n    const stopCapture = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[stopCapture]\": ()=>{\n            if (!audioEngineRef.current) return;\n            audioEngineRef.current.stopCapture();\n            if (levelIntervalRef.current) {\n                clearInterval(levelIntervalRef.current);\n                levelIntervalRef.current = null;\n            }\n            outputStreamRef.current = null;\n            setState({\n                \"useSystemAudio.useCallback[stopCapture]\": (prev)=>({\n                        ...prev,\n                        isCapturing: false,\n                        audioLevel: 0\n                    })\n            }[\"useSystemAudio.useCallback[stopCapture]\"]);\n            console.log('⏹️ System audio capture stopped');\n        }\n    }[\"useSystemAudio.useCallback[stopCapture]\"], []);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[setVolume]\": (volume)=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            audioEngineRef.current.setVolume(volume);\n            setState({\n                \"useSystemAudio.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume\n                    })\n            }[\"useSystemAudio.useCallback[setVolume]\"]);\n        }\n    }[\"useSystemAudio.useCallback[setVolume]\"], [\n        state.isInitialized\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1) return;\n            audioEngineRef.current.setEQBand(freqIndex, gain);\n            setState({\n                \"useSystemAudio.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: gain\n                        }\n                    })\n            }[\"useSystemAudio.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useSystemAudio.useCallback[setEQFrequency]\"], [\n        state.isInitialized,\n        state.frequencies\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[resetEQ]\": ()=>{\n            if (!audioEngineRef.current || !state.isInitialized) return;\n            audioEngineRef.current.resetEQ();\n            setState({\n                \"useSystemAudio.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useSystemAudio.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useSystemAudio.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useSystemAudio.useCallback[resetEQ]\"]);\n        }\n    }[\"useSystemAudio.useCallback[resetEQ]\"], [\n        state.isInitialized\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[getAnalyserData]\": ()=>{\n            if (!audioEngineRef.current || !state.isInitialized) {\n                return new Uint8Array(0);\n            }\n            return audioEngineRef.current.getAnalyserData();\n        }\n    }[\"useSystemAudio.useCallback[getAnalyserData]\"], [\n        state.isInitialized\n    ]);\n    const setInputDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSystemAudio.useCallback[setInputDevice]\": (deviceId)=>{\n            setState({\n                \"useSystemAudio.useCallback[setInputDevice]\": (prev)=>({\n                        ...prev,\n                        selectedInputDevice: deviceId\n                    })\n            }[\"useSystemAudio.useCallback[setInputDevice]\"]);\n        }\n    }[\"useSystemAudio.useCallback[setInputDevice]\"], []);\n    // Cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSystemAudio.useEffect\": ()=>{\n            return ({\n                \"useSystemAudio.useEffect\": ()=>{\n                    if (levelIntervalRef.current) {\n                        clearInterval(levelIntervalRef.current);\n                    }\n                    if (audioEngineRef.current) {\n                        audioEngineRef.current.destroy();\n                    }\n                }\n            })[\"useSystemAudio.useEffect\"];\n        }\n    }[\"useSystemAudio.useEffect\"], []);\n    return {\n        ...state,\n        initializeAudio,\n        startCapture,\n        stopCapture,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData,\n        setInputDevice,\n        outputStream: outputStreamRef.current\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSystemAudio.ts\n"));

/***/ })

});