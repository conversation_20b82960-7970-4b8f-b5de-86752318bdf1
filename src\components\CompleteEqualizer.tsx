'use client'

import { useState } from 'react'
import SystemEqualizer from './SystemEqualizer'
import ScreenAudioCapture from './ScreenAudioCapture'
import EqualizerNew from './EqualizerNew'

type AudioMode = 'microphone' | 'screen' | 'test'

export default function CompleteEqualizer() {
  const [activeMode, setActiveMode] = useState<AudioMode>('microphone')

  const modes = [
    {
      id: 'microphone' as AudioMode,
      name: 'Microphone/Ligne',
      icon: '🎤',
      description: 'Capture audio depuis microphone ou entrée ligne',
      recommended: true
    },
    {
      id: 'screen' as AudioMode,
      name: 'Audio Système',
      icon: '🖥️',
      description: 'Capture l\'audio système complet (expérimental)',
      recommended: false
    },
    {
      id: 'test' as AudioMode,
      name: 'Mode Test',
      icon: '🔊',
      description: 'Générateur de sons pour tester l\'égaliseur',
      recommended: false
    }
  ]

  const renderContent = () => {
    switch (activeMode) {
      case 'microphone':
        return <SystemEqualizer />
      case 'screen':
        return <ScreenAudioCapture />
      case 'test':
        return <EqualizerNew />
      default:
        return <SystemEqualizer />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">WaveCraft - Égaliseur Audio</h1>
        <p className="text-gray-400">Traitement audio en temps réel avec intelligence artificielle</p>
      </div>

      {/* Mode Selection */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Mode de Capture Audio</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {modes.map((mode) => (
            <button
              key={mode.id}
              onClick={() => setActiveMode(mode.id)}
              className={`p-4 rounded-lg border-2 transition-all text-left ${
                activeMode === mode.id
                  ? 'border-blue-500 bg-blue-600/10'
                  : 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
              }`}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{mode.icon}</span>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-white">{mode.name}</h4>
                    {mode.recommended && (
                      <span className="px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs">
                        Recommandé
                      </span>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm mt-1">{mode.description}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Mode-specific Instructions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Instructions - {modes.find(m => m.id === activeMode)?.name}</h3>
        <div className="space-y-3 text-sm text-gray-300">
          {activeMode === 'microphone' && (
            <>
              <div className="p-3 bg-blue-600/10 border border-blue-600/20 rounded">
                <p className="text-blue-300 font-medium mb-2">🎤 Mode Microphone/Ligne (Recommandé)</p>
                <ul className="space-y-1 text-blue-200">
                  <li>• Connectez votre microphone ou source audio à l'entrée ligne</li>
                  <li>• Sélectionnez le bon périphérique d'entrée</li>
                  <li>• Cliquez sur "Démarrer Capture" pour commencer</li>
                  <li>• L'audio sera traité en temps réel et renvoyé aux haut-parleurs</li>
                  <li>• Ajustez l'égaliseur pendant la lecture</li>
                </ul>
              </div>
              <p><strong>Cas d'usage :</strong> Podcasting, streaming, musique live, instruments</p>
            </>
          )}
          
          {activeMode === 'screen' && (
            <>
              <div className="p-3 bg-yellow-600/10 border border-yellow-600/20 rounded">
                <p className="text-yellow-300 font-medium mb-2">🖥️ Mode Audio Système (Expérimental)</p>
                <ul className="space-y-1 text-yellow-200">
                  <li>• Fonctionne uniquement sur Chrome/Edge (pas Firefox)</li>
                  <li>• Cliquez sur "Capturer Audio Système"</li>
                  <li>• Sélectionnez "Partager l'audio système" dans la popup</li>
                  <li>• L'audio de YouTube, Spotify, etc. sera traité</li>
                  <li>• ⚠️ Peut causer des boucles audio - utilisez un casque</li>
                </ul>
              </div>
              <p><strong>Cas d'usage :</strong> Améliorer l'audio de YouTube, Netflix, jeux, musique</p>
            </>
          )}
          
          {activeMode === 'test' && (
            <>
              <div className="p-3 bg-green-600/10 border border-green-600/20 rounded">
                <p className="text-green-300 font-medium mb-2">🔊 Mode Test</p>
                <ul className="space-y-1 text-green-200">
                  <li>• Utilisez le générateur de sons intégré</li>
                  <li>• Testez différentes fréquences (440Hz par défaut)</li>
                  <li>• Parfait pour calibrer et tester l'égaliseur</li>
                  <li>• Aucune source audio externe requise</li>
                </ul>
              </div>
              <p><strong>Cas d'usage :</strong> Test et calibration de l'égaliseur, démonstration</p>
            </>
          )}
        </div>
      </div>

      {/* Active Mode Content */}
      {renderContent()}

      {/* Technical Info */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Informations Techniques</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-white mb-2">Spécifications Audio</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• <strong>Fréquences :</strong> 10 bandes (32Hz - 16kHz)</li>
              <li>• <strong>Plage EQ :</strong> -12dB à +12dB</li>
              <li>• <strong>Volume :</strong> 0% à 200% (avec amplification)</li>
              <li>• <strong>Latence :</strong> ~10-20ms (Web Audio API)</li>
              <li>• <strong>Qualité :</strong> 44.1kHz, stéréo</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-2">Compatibilité</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• <strong>Chrome/Edge :</strong> ✅ Toutes fonctionnalités</li>
              <li>• <strong>Firefox :</strong> ⚠️ Microphone uniquement</li>
              <li>• <strong>Safari :</strong> ⚠️ Support limité</li>
              <li>• <strong>Mobile :</strong> 📱 Microphone uniquement</li>
              <li>• <strong>HTTPS requis :</strong> Pour accès microphone</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">💡 Conseils d'Utilisation</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-white mb-2">Pour de meilleurs résultats :</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Utilisez un casque pour éviter les boucles audio</li>
              <li>• Ajustez le volume d'entrée avant l'égaliseur</li>
              <li>• Surveillez le niveau audio (évitez la saturation)</li>
              <li>• Testez avec différents types de musique</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-2">Dépannage :</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Pas de son → Vérifiez les permissions microphone</li>
              <li>• Écho → Utilisez un casque ou baissez le volume</li>
              <li>• Latence → Fermez les autres onglets audio</li>
              <li>• Qualité → Vérifiez la qualité du microphone</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
