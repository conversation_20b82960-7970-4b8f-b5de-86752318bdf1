"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard.tsx":
/*!**************************************!*\
  !*** ./src/components/Dashboard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AudioVisualizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AudioVisualizer */ \"(app-pages-browser)/./src/components/AudioVisualizer.tsx\");\n/* harmony import */ var _QuickStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuickStats */ \"(app-pages-browser)/./src/components/QuickStats.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Dashboard() {\n    _s();\n    const [currentTrack, setCurrentTrack] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: 'Aucune musique détectée',\n        artist: 'En attente...',\n        genre: 'Inconnu',\n        confidence: 0\n    });\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const tracks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Dashboard.useMemo[tracks]\": ()=>[\n                {\n                    title: 'Bohemian Rhapsody',\n                    artist: 'Queen',\n                    genre: 'Rock',\n                    confidence: 95\n                },\n                {\n                    title: 'Billie Jean',\n                    artist: 'Michael Jackson',\n                    genre: 'Pop',\n                    confidence: 92\n                },\n                {\n                    title: 'Hotel California',\n                    artist: 'Eagles',\n                    genre: 'Rock',\n                    confidence: 88\n                },\n                {\n                    title: 'Imagine',\n                    artist: 'John Lennon',\n                    genre: 'Folk',\n                    confidence: 90\n                }\n            ]\n    }[\"Dashboard.useMemo[tracks]\"], []);\n    const updateTrack = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Dashboard.useCallback[updateTrack]\": ()=>{\n            if (isListening) {\n                const randomTrack = tracks[Math.floor(Math.random() * tracks.length)];\n                setCurrentTrack(randomTrack);\n            }\n        }\n    }[\"Dashboard.useCallback[updateTrack]\"], [\n        isListening,\n        tracks\n    ]);\n    // Simulation de détection audio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const interval = setInterval(updateTrack, 4000) // Plus rapide\n            ;\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        updateTrack\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Vue d'ensemble de votre exp\\xe9rience audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsListening(!isListening),\n                        className: \"px-6 py-3 rounded-lg font-medium transition-colors \".concat(isListening ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'),\n                        children: isListening ? '⏸️ Arrêter l\\'écoute' : '▶️ Démarrer l\\'écoute'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83C\\uDFB5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: currentTrack.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: currentTrack.artist\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-primary-600/20 text-primary-300 rounded-full text-sm\",\n                                            children: currentTrack.genre\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                \"Confiance: \",\n                                                currentTrack.confidence,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full \".concat(isListening ? 'bg-green-500' : 'bg-gray-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: isListening ? 'En écoute' : 'Arrêté'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Visualisation Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AudioVisualizer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        isActive: isListening\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Activit\\xe9 R\\xe9cente\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            {\n                                time: '14:32',\n                                action: 'Égaliseur ajusté automatiquement',\n                                track: 'Bohemian Rhapsody'\n                            },\n                            {\n                                time: '14:28',\n                                action: 'Nouveau genre détecté: Rock',\n                                track: 'Hotel California'\n                            },\n                            {\n                                time: '14:25',\n                                action: 'Préférences mises à jour',\n                                track: 'Billie Jean'\n                            },\n                            {\n                                time: '14:20',\n                                action: 'Session d\\'écoute démarrée',\n                                track: '-'\n                            }\n                        ].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-dark-700/50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-sm\",\n                                                children: activity.action\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: activity.track\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: activity.time\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"uo/AGYI9+Ymqv9XfJzcAfgTx8x8=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard.tsx\n"));

/***/ })

});