'use client'

import { useEffect, useState, useRef } from 'react'

interface AudioVisualizerNewProps {
  isActive: boolean
  getAnalyserData?: () => Uint8Array
  title?: string
}

export default function AudioVisualizerNew({ 
  isActive, 
  getAnalyserData, 
  title = "Visualisation Audio" 
}: AudioVisualizerNewProps) {
  const [bars, setBars] = useState<number[]>(new Array(10).fill(0))
  const animationRef = useRef<number>()

  useEffect(() => {
    const updateBars = () => {
      if (isActive && getAnalyserData) {
        const dataArray = getAnalyserData()
        if (dataArray.length > 0) {
          // Prendre 10 échantillons répartis sur le spectre
          const newBars = []
          const step = Math.floor(dataArray.length / 10)
          
          for (let i = 0; i < 10; i++) {
            const index = i * step
            const value = dataArray[index] || 0
            // Convertir de 0-255 à 0-100
            newBars.push((value / 255) * 100)
          }
          setBars(newBars)
        } else {
          // Fallback vers simulation si pas de données
          setBars(prev => prev.map(() => Math.random() * 80 + 10))
        }
      } else {
        setBars(new Array(10).fill(0))
      }

      if (isActive) {
        animationRef.current = requestAnimationFrame(updateBars)
      }
    }

    if (isActive) {
      updateBars()
    } else {
      setBars(new Array(10).fill(0))
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isActive, getAnalyserData])

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
      <div className="h-32 flex items-end justify-center space-x-2 bg-gray-800 rounded p-4">
        {bars.map((height, index) => (
          <div
            key={index}
            className="w-4 bg-blue-500 rounded-t"
            style={{
              height: `${Math.max(height, 5)}%`,
            }}
          />
        ))}
      </div>
      <div className="mt-2 text-xs text-gray-400 text-center">
        {isActive ? 'Analyse audio en temps réel' : 'Aucune activité audio'}
      </div>
    </div>
  )
}
