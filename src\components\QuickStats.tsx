'use client'

export default function QuickStats() {
  const stats = [
    {
      label: 'Morceaux analysés',
      value: '1,247',
      change: '+12%',
      icon: '🎵',
      color: 'text-blue-400'
    },
    {
      label: 'Genres détectés',
      value: '23',
      change: '+3',
      icon: '🎭',
      color: 'text-green-400'
    },
    {
      label: 'Précision IA',
      value: '94.2%',
      change: '+2.1%',
      icon: '🧠',
      color: 'text-purple-400'
    },
    {
      label: 'Temps d\'écoute',
      value: '47h',
      change: '+8h',
      icon: '⏱️',
      color: 'text-orange-400'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <div key={index} className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">{stat.label}</p>
              <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
              <p className={`text-sm mt-1 ${stat.color}`}>
                {stat.change} cette semaine
              </p>
            </div>
            <div className="text-3xl">{stat.icon}</div>
          </div>
        </div>
      ))}
    </div>
  )
}
