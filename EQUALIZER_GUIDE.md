# Guide d'utilisation de l'Égaliseur WaveCraft

## 🎛️ Fonctionnalités implémentées

### ✅ Égaliseur Manuel
- **10 bandes de fréquences** : 32Hz, 64Hz, 125Hz, 250Hz, 500Hz, 1kHz, 2kHz, 4kHz, 8kHz, 16kHz
- **Plage d'ajustement** : -10dB à +10dB pour chaque bande
- **Filtres audio réels** : Utilise l'API Web Audio avec des filtres BiquadFilter
- **Réponse en temps réel** : Les changements sont appliqués instantanément

### ✅ Contrôle du Volume
- **Volume principal** : 0% à 100%
- **Contrôle en temps réel** : Utilise un GainNode de l'API Web Audio
- **Affichage dynamique** : Pourcentage affiché en temps réel

### ✅ Presets d'Égaliseur
- **Manuel** : Réglages personnalisés
- **Plat** : Aucun ajustement (0dB partout)
- **Rock** : Basses et aigus renforcés
- **Pop** : Équilibré avec médiums légèrement boostés
- **Classique** : Naturel avec aigus légèrement atténués
- **Jazz** : Médiums chauds, basses légères
- **Électronique** : Basses profondes, aigus présents
- **Vocal** : Optimisé pour les voix (médiums boostés)

### ✅ Visualiseur Audio
- **Analyse spectrale en temps réel** : Utilise AnalyserNode
- **10 barres de fréquences** : Représentation visuelle du spectre audio
- **Données réelles** : Affiche les vraies données audio quand disponibles
- **Fallback** : Simulation si pas de source audio

## 🚀 Comment utiliser

### 1. Accéder à l'Égaliseur
- Cliquez sur "Égaliseur" dans la barre latérale
- L'interface se charge avec le contexte audio

### 2. Tester l'Audio
- Utilisez le bouton "🔊 Test Son" pour générer un son de test (440Hz pendant 2 secondes)
- Ou connectez une source audio externe

### 3. Ajuster l'Égaliseur
- **Mode Manuel** : Déplacez les sliders de fréquences
- **Presets** : Cliquez sur un preset pour appliquer des réglages prédéfinis
- **Reset** : Bouton pour remettre toutes les fréquences à 0dB

### 4. Contrôler le Volume
- Utilisez le slider "Volume Principal"
- Le pourcentage s'affiche en temps réel

## 🔧 Détails techniques

### Architecture Audio
```
Source Audio → GainNode → EQ Filters (10x) → AnalyserNode → Destination
```

### Filtres utilisés
- **32Hz** : Lowshelf filter (filtre passe-bas avec plateau)
- **64Hz-8kHz** : Peaking filters (filtres en cloche)
- **16kHz** : Highshelf filter (filtre passe-haut avec plateau)

### API Web Audio
- **AudioContext** : Contexte audio principal
- **BiquadFilterNode** : Filtres d'égaliseur
- **GainNode** : Contrôle du volume
- **AnalyserNode** : Analyse spectrale pour visualisation

## 🎯 Prochaines étapes

### À implémenter
- [ ] Capture audio système (microphone/ligne d'entrée)
- [ ] Sauvegarde des presets personnalisés
- [ ] Égaliseur automatique par IA
- [ ] Support de fichiers audio locaux
- [ ] Effets audio supplémentaires (reverb, compresseur)

### Limitations actuelles
- Nécessite une interaction utilisateur pour initialiser l'AudioContext
- Pas de capture audio système automatique
- Presets fixes (pas de sauvegarde personnalisée)

## 🐛 Dépannage

### Problèmes courants
1. **"Contexte audio non initialisé"**
   - Solution : Cliquez sur "Test Son" ou interagissez avec l'interface

2. **Pas de son**
   - Vérifiez que le volume n'est pas à 0%
   - Vérifiez les paramètres audio du navigateur

3. **Visualiseur ne bouge pas**
   - Assurez-vous qu'une source audio est connectée
   - Le visualiseur utilise des données simulées par défaut

### Compatibilité navigateur
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari (avec limitations)
- ❌ Internet Explorer (non supporté)
