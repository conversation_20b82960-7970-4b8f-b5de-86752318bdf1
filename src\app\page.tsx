'use client'

import { useState, useCallback } from 'react'
import Sidebar from '@/components/Sidebar'
import Dashboard from '@/components/Dashboard'
import Equalizer from '@/components/Equalizer'
import AIAnalysis from '@/components/AIAnalysis'
import History from '@/components/History'
import Settings from '@/components/Settings'
import Learning from '@/components/Learning'
import PageTransition from '@/components/PageTransition'

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <PageTransition><Dashboard /></PageTransition>
      case 'equalizer':
        return <PageTransition><Equalizer /></PageTransition>
      case 'ai-analysis':
        return <PageTransition><AIAnalysis /></PageTransition>
      case 'history':
        return <PageTransition><History /></PageTransition>
      case 'settings':
        return <PageTransition><Settings /></PageTransition>
      case 'learning':
        return <PageTransition><Learning /></PageTransition>
      default:
        return <PageTransition><Dashboard /></PageTransition>
    }
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar activeTab={activeTab} setActiveTab={handleTabChange} />
      <main className="flex-1 overflow-y-auto">
        <div className="p-6">
          {renderContent()}
        </div>
      </main>
    </div>
  )
}
