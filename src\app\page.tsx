'use client'

import { useState, useCallback } from 'react'
import Sidebar from '@/components/Sidebar'
import DashboardNew from '@/components/DashboardNew'
import EqualizerNew from '@/components/EqualizerNew'
import AIAnalysis from '@/components/AIAnalysis'
import History from '@/components/History'
import Settings from '@/components/Settings'
import Learning from '@/components/Learning'

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardNew />
      case 'equalizer':
        return <EqualizerNew />
      case 'ai-analysis':
        return <AIAnalysis />
      case 'history':
        return <History />
      case 'settings':
        return <Settings />
      case 'learning':
        return <Learning />
      default:
        return <DashboardNew />
    }
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar activeTab={activeTab} setActiveTab={handleTabChange} />
      <main className="flex-1 overflow-y-auto">
        <div className="p-6">
          {renderContent()}
        </div>
      </main>
    </div>
  )
}
