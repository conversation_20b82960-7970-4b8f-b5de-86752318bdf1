'use client'

import { useState } from 'react'
import Sidebar from '@/components/Sidebar'
import Dashboard from '@/components/Dashboard'
import Equalizer from '@/components/Equalizer'
import AIAnalysis from '@/components/AIAnalysis'
import History from '@/components/History'
import Settings from '@/components/Settings'
import Learning from '@/components/Learning'

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />
      case 'equalizer':
        return <Equalizer />
      case 'ai-analysis':
        return <AIAnalysis />
      case 'history':
        return <History />
      case 'settings':
        return <Settings />
      case 'learning':
        return <Learning />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      <main className="flex-1 overflow-y-auto">
        <div className="p-6">
          {renderContent()}
        </div>
      </main>
    </div>
  )
}
