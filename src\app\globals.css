@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-dark-900 text-white;
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .audio-wave {
    @apply bg-gradient-to-t from-primary-500 to-primary-300;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-150;
  }

  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-150;
  }

  .card {
    @apply glass-effect rounded-xl p-6 shadow-xl;
  }

  .nav-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-150 hover:bg-white/10;
  }

  .nav-item.active {
    @apply bg-primary-600/20 text-primary-300;
  }
}

/* Animations personnalisées */
@keyframes equalizer {
  0%, 100% { height: 20%; }
  25% { height: 60%; }
  50% { height: 100%; }
  75% { height: 40%; }
}

.equalizer-bar {
  animation: equalizer 0.6s ease-in-out infinite;
}

.equalizer-bar:nth-child(2) { animation-delay: 0.05s; }
.equalizer-bar:nth-child(3) { animation-delay: 0.1s; }
.equalizer-bar:nth-child(4) { animation-delay: 0.15s; }
.equalizer-bar:nth-child(5) { animation-delay: 0.2s; }

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}
