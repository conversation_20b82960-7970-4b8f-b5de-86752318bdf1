@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-dark-900 text-white;
  }
}

@layer components {
  .glass-effect {
    @apply bg-gray-800 border border-gray-700;
  }

  .gradient-bg {
    @apply bg-gray-800;
  }

  .audio-wave {
    @apply bg-blue-500;
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded;
  }

  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded;
  }

  .card {
    @apply bg-gray-800 border border-gray-700 rounded p-6;
  }

  .nav-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded hover:bg-gray-700;
  }

  .nav-item.active {
    @apply bg-blue-600 text-white;
  }
}

/* Animations simplifiées */
.equalizer-bar {
  background: #3b82f6;
}

/* Scrollbar simple */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
