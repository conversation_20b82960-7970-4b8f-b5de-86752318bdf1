"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        audioContext: null,\n        gainNode: null,\n        analyserNode: null,\n        eqNodes: [],\n        isInitialized: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    });\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const oscillatorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fréquences de l'égaliseur\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            if (state.isInitialized) return;\n            try {\n                console.log('Initializing audio context...');\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Reprendre le contexte si suspendu\n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully', {\n                    state: audioContext.state,\n                    sampleRate: audioContext.sampleRate\n                });\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], [\n        state.isInitialized\n    ]);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            console.log('Setting volume to:', volume);\n            if (!state.gainNode || !state.audioContext) {\n                console.log('No gain node or audio context available');\n                return;\n            }\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            try {\n                state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n                console.log('Volume set successfully:', gainValue);\n            } catch (error) {\n                console.error('Error setting volume:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            console.log('Setting EQ frequency:', freqKey, 'to gain:', gain);\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex] || !state.audioContext) {\n                console.log('EQ node not found or audio context not available');\n                return;\n            }\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            try {\n                eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n                console.log('EQ frequency set successfully:', freqKey, clampedGain);\n            } catch (error) {\n                console.error('Error setting EQ frequency:', error);\n            }\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    // Initialiser automatiquement au montage\n    useEffect({\n        \"useAudioContext.useEffect\": ()=>{\n            initializeAudioContext();\n        }\n    }[\"useAudioContext.useEffect\"], [\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});