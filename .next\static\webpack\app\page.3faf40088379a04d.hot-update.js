"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Equalizer.tsx":
/*!**************************************!*\
  !*** ./src/components/Equalizer.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Equalizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioContext */ \"(app-pages-browser)/./src/hooks/useAudioContext.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Equalizer() {\n    _s();\n    const [isAutoMode, setIsAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // Commencer en mode manuel\n    ;\n    const [selectedPreset, setSelectedPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('manual');\n    const { frequencies, volume, isInitialized, connectAudioElement, setVolume, setEQFrequency, resetEQ } = (0,_hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__.useAudioContext)();\n    const presets = [\n        {\n            id: 'auto',\n            name: 'Auto IA',\n            description: 'Ajustement automatique par IA'\n        },\n        {\n            id: 'rock',\n            name: 'Rock',\n            description: 'Basses et aigus renforcés'\n        },\n        {\n            id: 'pop',\n            name: 'Pop',\n            description: 'Équilibré pour la pop'\n        },\n        {\n            id: 'classical',\n            name: 'Classique',\n            description: 'Naturel et équilibré'\n        },\n        {\n            id: 'jazz',\n            name: 'Jazz',\n            description: 'Médiums chauds'\n        },\n        {\n            id: 'electronic',\n            name: 'Électronique',\n            description: 'Basses profondes'\n        },\n        {\n            id: 'vocal',\n            name: 'Vocal',\n            description: 'Optimisé pour les voix'\n        },\n        {\n            id: 'custom',\n            name: 'Personnalisé',\n            description: 'Réglages manuels'\n        }\n    ];\n    const handleFrequencyChange = (freq, value)=>{\n        if (!isAutoMode) {\n            setFrequencies((prev)=>({\n                    ...prev,\n                    [freq]: value\n                }));\n        }\n    };\n    const handlePresetChange = (presetId)=>{\n        setSelectedPreset(presetId);\n        setIsAutoMode(presetId === 'auto');\n        // Simulation de changement de preset\n        if (presetId === 'rock') {\n            setFrequencies({\n                '32Hz': 3,\n                '64Hz': 2,\n                '125Hz': 1,\n                '250Hz': 0,\n                '500Hz': -1,\n                '1kHz': 0,\n                '2kHz': 1,\n                '4kHz': 2,\n                '8kHz': 3,\n                '16kHz': 2\n            });\n        } else if (presetId === 'pop') {\n            setFrequencies({\n                '32Hz': 1,\n                '64Hz': 1,\n                '125Hz': 0,\n                '250Hz': 1,\n                '500Hz': 2,\n                '1kHz': 2,\n                '2kHz': 1,\n                '4kHz': 0,\n                '8kHz': 1,\n                '16kHz': 1\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: \"\\xc9galiseur Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Contr\\xf4lez et personnalisez votre exp\\xe9rience audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: \"Mode de fonctionnement\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm \".concat(!isAutoMode ? 'text-white' : 'text-gray-400'),\n                                        children: \"Manuel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsAutoMode(!isAutoMode),\n                                        className: \"relative w-12 h-6 rounded-full transition-colors \".concat(isAutoMode ? 'bg-primary-600' : 'bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform \".concat(isAutoMode ? 'translate-x-6' : 'translate-x-0.5')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm \".concat(isAutoMode ? 'text-white' : 'text-gray-400'),\n                                        children: \"Auto IA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    isAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-primary-600/10 border border-primary-600/20 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary-400\",\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-primary-300 text-sm\",\n                                    children: \"L'IA ajuste automatiquement l'\\xe9galiseur en fonction du contenu audio d\\xe9tect\\xe9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4\",\n                        children: \"Pr\\xe9r\\xe9glages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: presets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePresetChange(preset.id),\n                                className: \"p-3 rounded-lg text-left transition-colors \".concat(selectedPreset === preset.id ? 'bg-primary-600/20 border border-primary-600/40' : 'bg-dark-700/50 hover:bg-dark-600/50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: preset.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: preset.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, preset.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-6\",\n                        children: \"Fr\\xe9quences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 md:grid-cols-10 gap-4\",\n                        children: Object.entries(frequencies).map((param)=>{\n                            let [freq, value] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-medium\",\n                                        children: freq\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 w-8 bg-dark-700 rounded-full relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"-10\",\n                                                max: \"10\",\n                                                value: value,\n                                                onChange: (e)=>handleFrequencyChange(freq, parseInt(e.target.value)),\n                                                disabled: isAutoMode,\n                                                className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 w-full rounded \".concat(value >= 0 ? 'bg-blue-500' : 'bg-red-500'),\n                                                style: {\n                                                    height: \"\".concat(50 + value * 2.5, \"%\"),\n                                                    minHeight: '4px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute w-6 h-2 bg-white rounded\",\n                                                style: {\n                                                    bottom: \"\".concat(50 + value * 2.5 - 4, \"%\"),\n                                                    left: '50%',\n                                                    transform: 'translateX(-50%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white font-medium\",\n                                        children: [\n                                            value > 0 ? '+' : '',\n                                            value,\n                                            \"dB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, freq, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Volume Principal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDD0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0\",\n                                            max: \"100\",\n                                            defaultValue: \"75\",\n                                            className: \"flex-1 h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-medium w-12\",\n                                            children: \"75%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Effets Audio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    {\n                                        name: 'Bass Boost',\n                                        enabled: true\n                                    },\n                                    {\n                                        name: 'Virtualizer',\n                                        enabled: false\n                                    },\n                                    {\n                                        name: 'Reverb',\n                                        enabled: false\n                                    }\n                                ].map((effect, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: effect.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-6 rounded-full transition-colors \".concat(effect.enabled ? 'bg-primary-600' : 'bg-gray-600'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-white rounded-full transition-transform \".concat(effect.enabled ? 'translate-x-5' : 'translate-x-1')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\Equalizer.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(Equalizer, \"yAcXwZXb6MQnE+wpcGP4R0bJl9Y=\", false, function() {\n    return [\n        _hooks_useAudioContext__WEBPACK_IMPORTED_MODULE_2__.useAudioContext\n    ];\n});\n_c = Equalizer;\nvar _c;\n$RefreshReg$(_c, \"Equalizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0VxdWFsaXplci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU2QztBQUNZO0FBRzFDLFNBQVNFOztJQUN0QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQUMsT0FBTywyQkFBMkI7O0lBQy9FLE1BQU0sQ0FBQ0ssZ0JBQWdCQyxrQkFBa0IsR0FBR04sK0NBQVFBLENBQUM7SUFFckQsTUFBTSxFQUNKTyxXQUFXLEVBQ1hDLE1BQU0sRUFDTkMsYUFBYSxFQUNiQyxtQkFBbUIsRUFDbkJDLFNBQVMsRUFDVEMsY0FBYyxFQUNkQyxPQUFPLEVBQ1IsR0FBR1osdUVBQWVBO0lBRW5CLE1BQU1hLFVBQVU7UUFDZDtZQUFFQyxJQUFJO1lBQVFDLE1BQU07WUFBV0MsYUFBYTtRQUFnQztRQUM1RTtZQUFFRixJQUFJO1lBQVFDLE1BQU07WUFBUUMsYUFBYTtRQUE0QjtRQUNyRTtZQUFFRixJQUFJO1lBQU9DLE1BQU07WUFBT0MsYUFBYTtRQUF3QjtRQUMvRDtZQUFFRixJQUFJO1lBQWFDLE1BQU07WUFBYUMsYUFBYTtRQUF1QjtRQUMxRTtZQUFFRixJQUFJO1lBQVFDLE1BQU07WUFBUUMsYUFBYTtRQUFpQjtRQUMxRDtZQUFFRixJQUFJO1lBQWNDLE1BQU07WUFBZ0JDLGFBQWE7UUFBbUI7UUFDMUU7WUFBRUYsSUFBSTtZQUFTQyxNQUFNO1lBQVNDLGFBQWE7UUFBeUI7UUFDcEU7WUFBRUYsSUFBSTtZQUFVQyxNQUFNO1lBQWdCQyxhQUFhO1FBQW1CO0tBQ3ZFO0lBRUQsTUFBTUMsd0JBQXdCLENBQUNDLE1BQWNDO1FBQzNDLElBQUksQ0FBQ2pCLFlBQVk7WUFDZmtCLGVBQWVDLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDSCxLQUFLLEVBQUVDO2dCQUFNO1FBQ25EO0lBQ0Y7SUFFQSxNQUFNRyxxQkFBcUIsQ0FBQ0M7UUFDMUJsQixrQkFBa0JrQjtRQUNsQnBCLGNBQWNvQixhQUFhO1FBRTNCLHFDQUFxQztRQUNyQyxJQUFJQSxhQUFhLFFBQVE7WUFDdkJILGVBQWU7Z0JBQ2IsUUFBUTtnQkFDUixRQUFRO2dCQUNSLFNBQVM7Z0JBQ1QsU0FBUztnQkFDVCxTQUFTLENBQUM7Z0JBQ1YsUUFBUTtnQkFDUixRQUFRO2dCQUNSLFFBQVE7Z0JBQ1IsUUFBUTtnQkFDUixTQUFTO1lBQ1g7UUFDRixPQUFPLElBQUlHLGFBQWEsT0FBTztZQUM3QkgsZUFBZTtnQkFDYixRQUFRO2dCQUNSLFFBQVE7Z0JBQ1IsU0FBUztnQkFDVCxTQUFTO2dCQUNULFNBQVM7Z0JBQ1QsUUFBUTtnQkFDUixRQUFRO2dCQUNSLFFBQVE7Z0JBQ1IsUUFBUTtnQkFDUixTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDs7a0NBQ0MsOERBQUNFO3dCQUFHRCxXQUFVO2tDQUFxQzs7Ozs7O2tDQUNuRCw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7MEJBSS9CLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQUdILFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNJO3dDQUFLSixXQUFXLFdBQXdELE9BQTdDLENBQUN2QixhQUFhLGVBQWU7a0RBQW1COzs7Ozs7a0RBQzVFLDhEQUFDNEI7d0NBQ0NDLFNBQVMsSUFBTTVCLGNBQWMsQ0FBQ0Q7d0NBQzlCdUIsV0FBVyxvREFFVixPQURDdkIsYUFBYSxtQkFBbUI7a0RBR2xDLDRFQUFDc0I7NENBQUlDLFdBQVcsdUVBRWYsT0FEQ3ZCLGFBQWEsa0JBQWtCOzs7Ozs7Ozs7OztrREFHbkMsOERBQUMyQjt3Q0FBS0osV0FBVyxXQUF1RCxPQUE1Q3ZCLGFBQWEsZUFBZTtrREFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFJOUVBLDRCQUNDLDhEQUFDc0I7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQUtKLFdBQVU7OENBQW1COzs7Ozs7OENBQ25DLDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBMkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNoRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaWixRQUFRbUIsR0FBRyxDQUFDLENBQUNDLHVCQUNaLDhEQUFDSDtnQ0FFQ0MsU0FBUyxJQUFNVCxtQkFBbUJXLE9BQU9uQixFQUFFO2dDQUMzQ1csV0FBVyw4Q0FJVixPQUhDckIsbUJBQW1CNkIsT0FBT25CLEVBQUUsR0FDeEIsbURBQ0E7O2tEQUdOLDhEQUFDVTt3Q0FBSUMsV0FBVTtrREFBa0NRLE9BQU9sQixJQUFJOzs7Ozs7a0RBQzVELDhEQUFDUzt3Q0FBSUMsV0FBVTtrREFBOEJRLE9BQU9qQixXQUFXOzs7Ozs7OytCQVQxRGlCLE9BQU9uQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWdCdEIsOERBQUNVO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0c7d0JBQUdILFdBQVU7a0NBQXdDOzs7Ozs7a0NBQ3RELDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWlMsT0FBT0MsT0FBTyxDQUFDN0IsYUFBYTBCLEdBQUcsQ0FBQztnQ0FBQyxDQUFDZCxNQUFNQyxNQUFNO2lEQUM3Qyw4REFBQ0s7Z0NBQWVDLFdBQVU7O2tEQUN4Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXFDUDs7Ozs7O2tEQUNwRCw4REFBQ007d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDVztnREFDQ0MsTUFBSztnREFDTEMsS0FBSTtnREFDSkMsS0FBSTtnREFDSnBCLE9BQU9BO2dEQUNQcUIsVUFBVSxDQUFDQyxJQUFNeEIsc0JBQXNCQyxNQUFNd0IsU0FBU0QsRUFBRUUsTUFBTSxDQUFDeEIsS0FBSztnREFDcEV5QixVQUFVMUM7Z0RBQ1Z1QixXQUFVOzs7Ozs7MERBRVosOERBQUNEO2dEQUNDQyxXQUFXLG9DQUVWLE9BRENOLFNBQVMsSUFBSSxnQkFBZ0I7Z0RBRS9CMEIsT0FBTztvREFDTEMsUUFBUSxHQUFzQixPQUFuQixLQUFNM0IsUUFBUSxLQUFLO29EQUM5QjRCLFdBQVc7Z0RBQ2I7Ozs7OzswREFFRiw4REFBQ3ZCO2dEQUNDQyxXQUFVO2dEQUNWb0IsT0FBTztvREFDTEcsUUFBUSxHQUEwQixPQUF2QixLQUFNN0IsUUFBUSxNQUFPLEdBQUU7b0RBQ2xDOEIsTUFBTTtvREFDTkMsV0FBVztnREFDYjs7Ozs7Ozs7Ozs7O2tEQUdKLDhEQUFDMUI7d0NBQUlDLFdBQVU7OzRDQUNaTixRQUFRLElBQUksTUFBTTs0Q0FBSUE7NENBQU07Ozs7Ozs7OytCQS9CdkJEOzs7Ozs7Ozs7Ozs7Ozs7OzswQkF1Q2hCLDhEQUFDTTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQUdILFdBQVU7MENBQXdDOzs7Ozs7MENBQ3RELDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FBS0osV0FBVTtzREFBVzs7Ozs7O3NEQUMzQiw4REFBQ1c7NENBQ0NDLE1BQUs7NENBQ0xDLEtBQUk7NENBQ0pDLEtBQUk7NENBQ0pZLGNBQWE7NENBQ2IxQixXQUFVOzs7Ozs7c0RBRVosOERBQUNJOzRDQUFLSixXQUFVO3NEQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUFHSCxXQUFVOzBDQUF3Qzs7Ozs7OzBDQUN0RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1o7b0NBQ0M7d0NBQUVWLE1BQU07d0NBQWNxQyxTQUFTO29DQUFLO29DQUNwQzt3Q0FBRXJDLE1BQU07d0NBQWVxQyxTQUFTO29DQUFNO29DQUN0Qzt3Q0FBRXJDLE1BQU07d0NBQVVxQyxTQUFTO29DQUFNO2lDQUNsQyxDQUFDcEIsR0FBRyxDQUFDLENBQUNxQixRQUFRQyxzQkFDYiw4REFBQzlCO3dDQUFnQkMsV0FBVTs7MERBQ3pCLDhEQUFDSTtnREFBS0osV0FBVTswREFBYzRCLE9BQU90QyxJQUFJOzs7Ozs7MERBQ3pDLDhEQUFDZTtnREFDQ0wsV0FBVywyQ0FFVixPQURDNEIsT0FBT0QsT0FBTyxHQUFHLG1CQUFtQjswREFHdEMsNEVBQUM1QjtvREFBSUMsV0FBVyxzREFFZixPQURDNEIsT0FBT0QsT0FBTyxHQUFHLGtCQUFrQjs7Ozs7Ozs7Ozs7O3VDQVIvQkU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFrQnhCO0dBdE53QnJEOztRQVlsQkQsbUVBQWVBOzs7S0FaR0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWxhIEthbGJvdXNzaVxcRGVza3RvcFxcRXZlcnlcXGZwXFxlZ2FsaXNldXJcXHNyY1xcY29tcG9uZW50c1xcRXF1YWxpemVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdWRpb0NvbnRleHQgfSBmcm9tICdAL2hvb2tzL3VzZUF1ZGlvQ29udGV4dCdcbmltcG9ydCBBdWRpb1BsYXllciBmcm9tICcuL0F1ZGlvUGxheWVyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFcXVhbGl6ZXIoKSB7XG4gIGNvbnN0IFtpc0F1dG9Nb2RlLCBzZXRJc0F1dG9Nb2RlXSA9IHVzZVN0YXRlKGZhbHNlKSAvLyBDb21tZW5jZXIgZW4gbW9kZSBtYW51ZWxcbiAgY29uc3QgW3NlbGVjdGVkUHJlc2V0LCBzZXRTZWxlY3RlZFByZXNldF0gPSB1c2VTdGF0ZSgnbWFudWFsJylcblxuICBjb25zdCB7XG4gICAgZnJlcXVlbmNpZXMsXG4gICAgdm9sdW1lLFxuICAgIGlzSW5pdGlhbGl6ZWQsXG4gICAgY29ubmVjdEF1ZGlvRWxlbWVudCxcbiAgICBzZXRWb2x1bWUsXG4gICAgc2V0RVFGcmVxdWVuY3ksXG4gICAgcmVzZXRFUVxuICB9ID0gdXNlQXVkaW9Db250ZXh0KClcblxuICBjb25zdCBwcmVzZXRzID0gW1xuICAgIHsgaWQ6ICdhdXRvJywgbmFtZTogJ0F1dG8gSUEnLCBkZXNjcmlwdGlvbjogJ0FqdXN0ZW1lbnQgYXV0b21hdGlxdWUgcGFyIElBJyB9LFxuICAgIHsgaWQ6ICdyb2NrJywgbmFtZTogJ1JvY2snLCBkZXNjcmlwdGlvbjogJ0Jhc3NlcyBldCBhaWd1cyByZW5mb3Jjw6lzJyB9LFxuICAgIHsgaWQ6ICdwb3AnLCBuYW1lOiAnUG9wJywgZGVzY3JpcHRpb246ICfDiXF1aWxpYnLDqSBwb3VyIGxhIHBvcCcgfSxcbiAgICB7IGlkOiAnY2xhc3NpY2FsJywgbmFtZTogJ0NsYXNzaXF1ZScsIGRlc2NyaXB0aW9uOiAnTmF0dXJlbCBldCDDqXF1aWxpYnLDqScgfSxcbiAgICB7IGlkOiAnamF6eicsIG5hbWU6ICdKYXp6JywgZGVzY3JpcHRpb246ICdNw6lkaXVtcyBjaGF1ZHMnIH0sXG4gICAgeyBpZDogJ2VsZWN0cm9uaWMnLCBuYW1lOiAnw4lsZWN0cm9uaXF1ZScsIGRlc2NyaXB0aW9uOiAnQmFzc2VzIHByb2ZvbmRlcycgfSxcbiAgICB7IGlkOiAndm9jYWwnLCBuYW1lOiAnVm9jYWwnLCBkZXNjcmlwdGlvbjogJ09wdGltaXPDqSBwb3VyIGxlcyB2b2l4JyB9LFxuICAgIHsgaWQ6ICdjdXN0b20nLCBuYW1lOiAnUGVyc29ubmFsaXPDqScsIGRlc2NyaXB0aW9uOiAnUsOpZ2xhZ2VzIG1hbnVlbHMnIH1cbiAgXVxuXG4gIGNvbnN0IGhhbmRsZUZyZXF1ZW5jeUNoYW5nZSA9IChmcmVxOiBzdHJpbmcsIHZhbHVlOiBudW1iZXIpID0+IHtcbiAgICBpZiAoIWlzQXV0b01vZGUpIHtcbiAgICAgIHNldEZyZXF1ZW5jaWVzKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZyZXFdOiB2YWx1ZSB9KSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVQcmVzZXRDaGFuZ2UgPSAocHJlc2V0SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFNlbGVjdGVkUHJlc2V0KHByZXNldElkKVxuICAgIHNldElzQXV0b01vZGUocHJlc2V0SWQgPT09ICdhdXRvJylcblxuICAgIC8vIFNpbXVsYXRpb24gZGUgY2hhbmdlbWVudCBkZSBwcmVzZXRcbiAgICBpZiAocHJlc2V0SWQgPT09ICdyb2NrJykge1xuICAgICAgc2V0RnJlcXVlbmNpZXMoe1xuICAgICAgICAnMzJIeic6IDMsXG4gICAgICAgICc2NEh6JzogMixcbiAgICAgICAgJzEyNUh6JzogMSxcbiAgICAgICAgJzI1MEh6JzogMCxcbiAgICAgICAgJzUwMEh6JzogLTEsXG4gICAgICAgICcxa0h6JzogMCxcbiAgICAgICAgJzJrSHonOiAxLFxuICAgICAgICAnNGtIeic6IDIsXG4gICAgICAgICc4a0h6JzogMyxcbiAgICAgICAgJzE2a0h6JzogMlxuICAgICAgfSlcbiAgICB9IGVsc2UgaWYgKHByZXNldElkID09PSAncG9wJykge1xuICAgICAgc2V0RnJlcXVlbmNpZXMoe1xuICAgICAgICAnMzJIeic6IDEsXG4gICAgICAgICc2NEh6JzogMSxcbiAgICAgICAgJzEyNUh6JzogMCxcbiAgICAgICAgJzI1MEh6JzogMSxcbiAgICAgICAgJzUwMEh6JzogMixcbiAgICAgICAgJzFrSHonOiAyLFxuICAgICAgICAnMmtIeic6IDEsXG4gICAgICAgICc0a0h6JzogMCxcbiAgICAgICAgJzhrSHonOiAxLFxuICAgICAgICAnMTZrSHonOiAxXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2PlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPsOJZ2FsaXNldXIgQXVkaW88L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+Q29udHLDtGxleiBldCBwZXJzb25uYWxpc2V6IHZvdHJlIGV4cMOpcmllbmNlIGF1ZGlvPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2RlIFRvZ2dsZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+TW9kZSBkZSBmb25jdGlvbm5lbWVudDwvaDM+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gJHshaXNBdXRvTW9kZSA/ICd0ZXh0LXdoaXRlJyA6ICd0ZXh0LWdyYXktNDAwJ31gfT5NYW51ZWw8L3NwYW4+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQXV0b01vZGUoIWlzQXV0b01vZGUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSB3LTEyIGgtNiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBpc0F1dG9Nb2RlID8gJ2JnLXByaW1hcnktNjAwJyA6ICdiZy1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdy01IGgtNSBiZy13aGl0ZSByb3VuZGVkLWZ1bGwgdG9wLTAuNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSAke1xuICAgICAgICAgICAgICAgIGlzQXV0b01vZGUgPyAndHJhbnNsYXRlLXgtNicgOiAndHJhbnNsYXRlLXgtMC41J1xuICAgICAgICAgICAgICB9YH0gLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSAke2lzQXV0b01vZGUgPyAndGV4dC13aGl0ZScgOiAndGV4dC1ncmF5LTQwMCd9YH0+QXV0byBJQTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge2lzQXV0b01vZGUgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLXByaW1hcnktNjAwLzEwIGJvcmRlciBib3JkZXItcHJpbWFyeS02MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTQwMFwiPvCfp6A8L3NwYW4+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIEwnSUEgYWp1c3RlIGF1dG9tYXRpcXVlbWVudCBsJ8OpZ2FsaXNldXIgZW4gZm9uY3Rpb24gZHUgY29udGVudSBhdWRpbyBkw6l0ZWN0w6lcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByZXNldHMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5QcsOpcsOpZ2xhZ2VzPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC0zXCI+XG4gICAgICAgICAge3ByZXNldHMubWFwKChwcmVzZXQpID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtwcmVzZXQuaWR9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVByZXNldENoYW5nZShwcmVzZXQuaWQpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyB0ZXh0LWxlZnQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBzZWxlY3RlZFByZXNldCA9PT0gcHJlc2V0LmlkXG4gICAgICAgICAgICAgICAgICA/ICdiZy1wcmltYXJ5LTYwMC8yMCBib3JkZXIgYm9yZGVyLXByaW1hcnktNjAwLzQwJ1xuICAgICAgICAgICAgICAgICAgOiAnYmctZGFyay03MDAvNTAgaG92ZXI6YmctZGFyay02MDAvNTAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGUgdGV4dC1zbVwiPntwcmVzZXQubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMVwiPntwcmVzZXQuZGVzY3JpcHRpb259PC9kaXY+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVxdWFsaXplciBTbGlkZXJzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+RnLDqXF1ZW5jZXM8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTUgbWQ6Z3JpZC1jb2xzLTEwIGdhcC00XCI+XG4gICAgICAgICAge09iamVjdC5lbnRyaWVzKGZyZXF1ZW5jaWVzKS5tYXAoKFtmcmVxLCB2YWx1ZV0pID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtmcmVxfSBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgZm9udC1tZWRpdW1cIj57ZnJlcX08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMyIHctOCBiZy1kYXJrLTcwMCByb3VuZGVkLWZ1bGwgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICBtaW49XCItMTBcIlxuICAgICAgICAgICAgICAgICAgbWF4PVwiMTBcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3ZhbHVlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGcmVxdWVuY3lDaGFuZ2UoZnJlcSwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0F1dG9Nb2RlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsIG9wYWNpdHktMCBjdXJzb3ItcG9pbnRlciBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgYm90dG9tLTAgdy1mdWxsIHJvdW5kZWQgJHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPj0gMCA/ICdiZy1ibHVlLTUwMCcgOiAnYmctcmVkLTUwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBgJHs1MCArICh2YWx1ZSAqIDIuNSl9JWAsXG4gICAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzRweCdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB3LTYgaC0yIGJnLXdoaXRlIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgYm90dG9tOiBgJHs1MCArICh2YWx1ZSAqIDIuNSkgLSA0fSVgLFxuICAgICAgICAgICAgICAgICAgICBsZWZ0OiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWCgtNTAlKSdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAge3ZhbHVlID4gMCA/ICcrJyA6ICcnfXt2YWx1ZX1kQlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVm9sdW1lIGFuZCBFZmZlY3RzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+Vm9sdW1lIFByaW5jaXBhbDwvaDM+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+8J+Uijwvc3Bhbj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZT1cIjc1XCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgaC0yIGJnLWRhcmstNzAwIHJvdW5kZWQtbGcgYXBwZWFyYW5jZS1ub25lIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bSB3LTEyXCI+NzUlPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+RWZmZXRzIEF1ZGlvPC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgeyBuYW1lOiAnQmFzcyBCb29zdCcsIGVuYWJsZWQ6IHRydWUgfSxcbiAgICAgICAgICAgICAgeyBuYW1lOiAnVmlydHVhbGl6ZXInLCBlbmFibGVkOiBmYWxzZSB9LFxuICAgICAgICAgICAgICB7IG5hbWU6ICdSZXZlcmInLCBlbmFibGVkOiBmYWxzZSB9XG4gICAgICAgICAgICBdLm1hcCgoZWZmZWN0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57ZWZmZWN0Lm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMTAgaC02IHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBlZmZlY3QuZW5hYmxlZCA/ICdiZy1wcmltYXJ5LTYwMCcgOiAnYmctZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNCBoLTQgYmctd2hpdGUgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7XG4gICAgICAgICAgICAgICAgICAgIGVmZmVjdC5lbmFibGVkID8gJ3RyYW5zbGF0ZS14LTUnIDogJ3RyYW5zbGF0ZS14LTEnXG4gICAgICAgICAgICAgICAgICB9YH0gLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQXVkaW9Db250ZXh0IiwiRXF1YWxpemVyIiwiaXNBdXRvTW9kZSIsInNldElzQXV0b01vZGUiLCJzZWxlY3RlZFByZXNldCIsInNldFNlbGVjdGVkUHJlc2V0IiwiZnJlcXVlbmNpZXMiLCJ2b2x1bWUiLCJpc0luaXRpYWxpemVkIiwiY29ubmVjdEF1ZGlvRWxlbWVudCIsInNldFZvbHVtZSIsInNldEVRRnJlcXVlbmN5IiwicmVzZXRFUSIsInByZXNldHMiLCJpZCIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImhhbmRsZUZyZXF1ZW5jeUNoYW5nZSIsImZyZXEiLCJ2YWx1ZSIsInNldEZyZXF1ZW5jaWVzIiwicHJldiIsImhhbmRsZVByZXNldENoYW5nZSIsInByZXNldElkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaDMiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsIm1hcCIsInByZXNldCIsIk9iamVjdCIsImVudHJpZXMiLCJpbnB1dCIsInR5cGUiLCJtaW4iLCJtYXgiLCJvbkNoYW5nZSIsImUiLCJwYXJzZUludCIsInRhcmdldCIsImRpc2FibGVkIiwic3R5bGUiLCJoZWlnaHQiLCJtaW5IZWlnaHQiLCJib3R0b20iLCJsZWZ0IiwidHJhbnNmb3JtIiwiZGVmYXVsdFZhbHVlIiwiZW5hYmxlZCIsImVmZmVjdCIsImluZGV4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Equalizer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAudioContext.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAudioContext.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioContext: () => (/* binding */ useAudioContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioContext auto */ \nconst useAudioContext = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        audioContext: null,\n        gainNode: null,\n        analyserNode: null,\n        eqNodes: [],\n        isInitialized: false,\n        volume: 75,\n        frequencies: {\n            '32Hz': 0,\n            '64Hz': 0,\n            '125Hz': 0,\n            '250Hz': 0,\n            '500Hz': 0,\n            '1kHz': 0,\n            '2kHz': 0,\n            '4kHz': 0,\n            '8kHz': 0,\n            '16kHz': 0\n        }\n    });\n    const sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fréquences de l'égaliseur\n    const eqFrequencies = [\n        32,\n        64,\n        125,\n        250,\n        500,\n        1000,\n        2000,\n        4000,\n        8000,\n        16000\n    ];\n    const initializeAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[initializeAudioContext]\": async ()=>{\n            try {\n                // Créer le contexte audio\n                const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                // Créer les nœuds\n                const gainNode = audioContext.createGain();\n                const analyserNode = audioContext.createAnalyser();\n                // Créer les filtres d'égaliseur\n                const eqNodes = eqFrequencies.map({\n                    \"useAudioContext.useCallback[initializeAudioContext].eqNodes\": (freq, index)=>{\n                        const filter = audioContext.createBiquadFilter();\n                        filter.type = index === 0 ? 'lowshelf' : index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking';\n                        filter.frequency.value = freq;\n                        filter.Q.value = 1;\n                        filter.gain.value = 0;\n                        return filter;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext].eqNodes\"]);\n                // Connecter les nœuds en chaîne\n                let previousNode = gainNode;\n                eqNodes.forEach({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (node)=>{\n                        previousNode.connect(node);\n                        previousNode = node;\n                    }\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                previousNode.connect(analyserNode);\n                analyserNode.connect(audioContext.destination);\n                // Configurer l'analyseur\n                analyserNode.fftSize = 256;\n                analyserNode.smoothingTimeConstant = 0.8;\n                // Définir le volume initial\n                gainNode.gain.value = 0.75;\n                setState({\n                    \"useAudioContext.useCallback[initializeAudioContext]\": (prev)=>({\n                            ...prev,\n                            audioContext,\n                            gainNode,\n                            analyserNode,\n                            eqNodes,\n                            isInitialized: true\n                        })\n                }[\"useAudioContext.useCallback[initializeAudioContext]\"]);\n                console.log('Audio context initialized successfully');\n            } catch (error) {\n                console.error('Failed to initialize audio context:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[initializeAudioContext]\"], []);\n    const connectAudioElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[connectAudioElement]\": (audioElement)=>{\n            if (!state.audioContext || !state.gainNode) return;\n            try {\n                // Déconnecter l'ancien source si il existe\n                if (sourceNodeRef.current) {\n                    sourceNodeRef.current.disconnect();\n                }\n                // Créer un nouveau source node\n                const sourceNode = state.audioContext.createMediaElementSource(audioElement);\n                sourceNode.connect(state.gainNode);\n                sourceNodeRef.current = sourceNode;\n                audioElementRef.current = audioElement;\n                console.log('Audio element connected');\n            } catch (error) {\n                console.error('Failed to connect audio element:', error);\n            }\n        }\n    }[\"useAudioContext.useCallback[connectAudioElement]\"], [\n        state.audioContext,\n        state.gainNode\n    ]);\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setVolume]\": (volume)=>{\n            if (!state.gainNode) return;\n            const clampedVolume = Math.max(0, Math.min(100, volume));\n            const gainValue = clampedVolume / 100;\n            state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime);\n            setState({\n                \"useAudioContext.useCallback[setVolume]\": (prev)=>({\n                        ...prev,\n                        volume: clampedVolume\n                    })\n            }[\"useAudioContext.useCallback[setVolume]\"]);\n        }\n    }[\"useAudioContext.useCallback[setVolume]\"], [\n        state.gainNode,\n        state.audioContext\n    ]);\n    const setEQFrequency = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[setEQFrequency]\": (freqKey, gain)=>{\n            const freqIndex = Object.keys(state.frequencies).indexOf(freqKey);\n            if (freqIndex === -1 || !state.eqNodes[freqIndex]) return;\n            const clampedGain = Math.max(-10, Math.min(10, gain));\n            const eqNode = state.eqNodes[freqIndex];\n            eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime);\n            setState({\n                \"useAudioContext.useCallback[setEQFrequency]\": (prev)=>({\n                        ...prev,\n                        frequencies: {\n                            ...prev.frequencies,\n                            [freqKey]: clampedGain\n                        }\n                    })\n            }[\"useAudioContext.useCallback[setEQFrequency]\"]);\n        }\n    }[\"useAudioContext.useCallback[setEQFrequency]\"], [\n        state.eqNodes,\n        state.frequencies,\n        state.audioContext\n    ]);\n    const resetEQ = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[resetEQ]\": ()=>{\n            state.eqNodes.forEach({\n                \"useAudioContext.useCallback[resetEQ]\": (node)=>{\n                    node.gain.setValueAtTime(0, state.audioContext.currentTime);\n                }\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n            setState({\n                \"useAudioContext.useCallback[resetEQ]\": (prev)=>({\n                        ...prev,\n                        frequencies: Object.keys(prev.frequencies).reduce({\n                            \"useAudioContext.useCallback[resetEQ]\": (acc, key)=>{\n                                acc[key] = 0;\n                                return acc;\n                            }\n                        }[\"useAudioContext.useCallback[resetEQ]\"], {})\n                    })\n            }[\"useAudioContext.useCallback[resetEQ]\"]);\n        }\n    }[\"useAudioContext.useCallback[resetEQ]\"], [\n        state.eqNodes,\n        state.audioContext\n    ]);\n    const getAnalyserData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioContext.useCallback[getAnalyserData]\": ()=>{\n            if (!state.analyserNode) return new Uint8Array(0);\n            const bufferLength = state.analyserNode.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            state.analyserNode.getByteFrequencyData(dataArray);\n            return dataArray;\n        }\n    }[\"useAudioContext.useCallback[getAnalyserData]\"], [\n        state.analyserNode\n    ]);\n    // Initialiser automatiquement au montage\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAudioContext.useEffect\": ()=>{\n            initializeAudioContext();\n        }\n    }[\"useAudioContext.useEffect\"], [\n        initializeAudioContext\n    ]);\n    return {\n        ...state,\n        initializeAudioContext,\n        connectAudioElement,\n        setVolume,\n        setEQFrequency,\n        resetEQ,\n        getAnalyserData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioContext.ts\n"));

/***/ })

});