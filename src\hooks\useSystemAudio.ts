'use client'

import { useState, useRef, useCallback, useEffect } from 'react'

interface SystemAudioState {
  isCapturing: boolean
  isInitialized: boolean
  volume: number
  frequencies: { [key: string]: number }
  error: string | null
  inputDevices: MediaDeviceInfo[]
  selectedInputDevice: string
  audioLevel: number
}

class SystemAudioEngine {
  private audioContext: AudioContext | null = null
  private gainNode: GainNode | null = null
  private analyserNode: AnalyserNode | null = null
  private eqNodes: BiquadFilterNode[] = []
  private sourceNode: MediaStreamAudioSourceNode | null = null
  private mediaStream: MediaStream | null = null
  private destinationNode: MediaStreamAudioDestinationNode | null = null
  
  private readonly eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]
  
  async initialize(): Promise<boolean> {
    try {
      console.log('🎵 Initializing System Audio Engine...')
      
      // Créer le contexte audio
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      
      // Créer les nœuds audio
      this.gainNode = this.audioContext.createGain()
      this.analyserNode = this.audioContext.createAnalyser()
      this.destinationNode = this.audioContext.createMediaStreamDestination()
      
      // Créer les filtres d'égaliseur
      this.eqNodes = this.eqFrequencies.map((freq, index) => {
        const filter = this.audioContext!.createBiquadFilter()
        
        if (index === 0) {
          filter.type = 'lowshelf'
        } else if (index === this.eqFrequencies.length - 1) {
          filter.type = 'highshelf'
        } else {
          filter.type = 'peaking'
        }
        
        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0
        
        return filter
      })
      
      // Configurer l'analyseur
      this.analyserNode.fftSize = 256
      this.analyserNode.smoothingTimeConstant = 0.8
      
      // Volume initial
      this.gainNode.gain.value = 1.0
      
      console.log('✅ System Audio Engine initialized')
      return true
      
    } catch (error) {
      console.error('❌ Failed to initialize System Audio Engine:', error)
      return false
    }
  }
  
  private connectAudioChain() {
    if (!this.sourceNode || !this.gainNode || !this.analyserNode || !this.destinationNode) return
    
    // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination
    let currentNode: AudioNode = this.sourceNode
    
    // Volume
    currentNode.connect(this.gainNode)
    currentNode = this.gainNode
    
    // Égaliseur
    this.eqNodes.forEach(eqNode => {
      currentNode.connect(eqNode)
      currentNode = eqNode
    })
    
    // Analyseur et sortie
    currentNode.connect(this.analyserNode)
    currentNode.connect(this.destinationNode)
    
    console.log('🔗 Audio chain connected')
  }
  
  async startCapture(deviceId?: string): Promise<MediaStream | null> {
    if (!this.audioContext) {
      throw new Error('Audio engine not initialized')
    }
    
    try {
      console.log('🎤 Starting audio capture...')
      
      // Configuration de capture
      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 44100,
          channelCount: 2
        },
        video: false
      }
      
      // Demander l'accès au microphone/audio système
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      
      // Créer le source node
      this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream)
      
      // Connecter la chaîne audio
      this.connectAudioChain()
      
      console.log('✅ Audio capture started')
      
      // Retourner le stream de sortie traité
      return this.destinationNode?.stream || null
      
    } catch (error) {
      console.error('❌ Error starting audio capture:', error)
      throw error
    }
  }
  
  stopCapture() {
    console.log('⏹️ Stopping audio capture...')
    
    if (this.sourceNode) {
      this.sourceNode.disconnect()
      this.sourceNode = null
    }
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop())
      this.mediaStream = null
    }
    
    console.log('✅ Audio capture stopped')
  }
  
  setVolume(volume: number) {
    if (!this.gainNode || !this.audioContext) return
    
    const clampedVolume = Math.max(0, Math.min(200, volume)) // 0-200% pour amplification
    const gainValue = clampedVolume / 100
    
    this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime)
    console.log(`🔊 System volume set to: ${clampedVolume}% (gain: ${gainValue})`)
  }
  
  setEQBand(bandIndex: number, gain: number) {
    if (!this.eqNodes[bandIndex] || !this.audioContext) return
    
    const clampedGain = Math.max(-12, Math.min(12, gain))
    this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime)
    
    const freq = this.eqFrequencies[bandIndex]
    console.log(`🎛️ System EQ ${freq}Hz set to: ${clampedGain}dB`)
  }
  
  resetEQ() {
    this.eqNodes.forEach((node) => {
      if (this.audioContext) {
        node.gain.setValueAtTime(0, this.audioContext.currentTime)
      }
    })
    console.log('🔄 System EQ reset to flat')
  }
  
  getAnalyserData(): Uint8Array {
    if (!this.analyserNode) return new Uint8Array(0)
    
    const bufferLength = this.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }
  
  getAudioLevel(): number {
    if (!this.analyserNode) return 0
    
    const bufferLength = this.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyserNode.getByteFrequencyData(dataArray)
    
    // Calculer le niveau moyen
    let sum = 0
    for (let i = 0; i < bufferLength; i++) {
      sum += dataArray[i]
    }
    
    return (sum / bufferLength) / 255 * 100
  }
  
  async getInputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return devices.filter(device => device.kind === 'audioinput')
    } catch (error) {
      console.error('❌ Error getting input devices:', error)
      return []
    }
  }
  
  destroy() {
    this.stopCapture()
    
    if (this.audioContext) {
      this.audioContext.close()
    }
    
    console.log('🗑️ System Audio Engine destroyed')
  }
}

export const useSystemAudio = () => {
  const [state, setState] = useState<SystemAudioState>({
    isCapturing: false,
    isInitialized: false,
    volume: 100,
    frequencies: {
      '32Hz': 0, '64Hz': 0, '125Hz': 0, '250Hz': 0, '500Hz': 0,
      '1kHz': 0, '2kHz': 0, '4kHz': 0, '8kHz': 0, '16kHz': 0
    },
    error: null,
    inputDevices: [],
    selectedInputDevice: '',
    audioLevel: 0
  })
  
  const audioEngineRef = useRef<SystemAudioEngine | null>(null)
  const outputStreamRef = useRef<MediaStream | null>(null)
  const levelIntervalRef = useRef<NodeJS.Timeout | null>(null)
  
  const initializeAudio = useCallback(async () => {
    if (state.isInitialized) return
    
    try {
      if (!audioEngineRef.current) {
        audioEngineRef.current = new SystemAudioEngine()
      }
      
      const success = await audioEngineRef.current.initialize()
      
      if (success) {
        // Récupérer les périphériques d'entrée
        const devices = await audioEngineRef.current.getInputDevices()
        
        setState(prev => ({
          ...prev,
          isInitialized: success,
          inputDevices: devices,
          selectedInputDevice: devices[0]?.deviceId || '',
          error: null
        }))
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to initialize system audio'
        }))
      }
      
    } catch (error) {
      console.error('❌ Failed to initialize system audio:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [state.isInitialized])
  
  const startCapture = useCallback(async () => {
    if (!audioEngineRef.current || !state.isInitialized) {
      await initializeAudio()
      if (!audioEngineRef.current) return
    }
    
    try {
      setState(prev => ({ ...prev, error: null }))
      
      const outputStream = await audioEngineRef.current.startCapture(state.selectedInputDevice)
      outputStreamRef.current = outputStream
      
      // Démarrer le monitoring du niveau audio
      levelIntervalRef.current = setInterval(() => {
        if (audioEngineRef.current) {
          const level = audioEngineRef.current.getAudioLevel()
          setState(prev => ({ ...prev, audioLevel: level }))
        }
      }, 100)
      
      setState(prev => ({ ...prev, isCapturing: true }))
      
      console.log('🎵 System audio capture started')
      
    } catch (error) {
      console.error('❌ Error starting capture:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to start capture'
      }))
    }
  }, [state.isInitialized, state.selectedInputDevice, initializeAudio])
  
  const stopCapture = useCallback(() => {
    if (!audioEngineRef.current) return
    
    audioEngineRef.current.stopCapture()
    
    if (levelIntervalRef.current) {
      clearInterval(levelIntervalRef.current)
      levelIntervalRef.current = null
    }
    
    outputStreamRef.current = null
    
    setState(prev => ({ 
      ...prev, 
      isCapturing: false,
      audioLevel: 0
    }))
    
    console.log('⏹️ System audio capture stopped')
  }, [])
  
  const setVolume = useCallback((volume: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    audioEngineRef.current.setVolume(volume)
    setState(prev => ({ ...prev, volume }))
  }, [state.isInitialized])
  
  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1) return
    
    audioEngineRef.current.setEQBand(freqIndex, gain)
    setState(prev => ({
      ...prev,
      frequencies: { ...prev.frequencies, [freqKey]: gain }
    }))
  }, [state.isInitialized, state.frequencies])
  
  const resetEQ = useCallback(() => {
    if (!audioEngineRef.current || !state.isInitialized) return
    
    audioEngineRef.current.resetEQ()
    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number })
    }))
  }, [state.isInitialized])
  
  const getAnalyserData = useCallback(() => {
    if (!audioEngineRef.current || !state.isInitialized) {
      return new Uint8Array(0)
    }
    
    return audioEngineRef.current.getAnalyserData()
  }, [state.isInitialized])
  
  const setInputDevice = useCallback((deviceId: string) => {
    setState(prev => ({ ...prev, selectedInputDevice: deviceId }))
  }, [])
  
  // Cleanup
  useEffect(() => {
    return () => {
      if (levelIntervalRef.current) {
        clearInterval(levelIntervalRef.current)
      }
      if (audioEngineRef.current) {
        audioEngineRef.current.destroy()
      }
    }
  }, [])

  return {
    ...state,
    initializeAudio,
    startCapture,
    stopCapture,
    setVolume,
    setEQFrequency,
    resetEQ,
    getAnalyserData,
    setInputDevice,
    outputStream: outputStreamRef.current
  }
}
