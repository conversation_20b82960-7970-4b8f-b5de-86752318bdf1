"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AudioVisualizer.tsx":
/*!********************************************!*\
  !*** ./src/components/AudioVisualizer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioVisualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AudioVisualizer(param) {\n    let { isActive, getAnalyserData } = param;\n    _s();\n    const [bars, setBars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(10).fill(0));\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioVisualizer.useEffect\": ()=>{\n            const updateBars = {\n                \"AudioVisualizer.useEffect.updateBars\": ()=>{\n                    if (isActive && getAnalyserData) {\n                        const dataArray = getAnalyserData();\n                        if (dataArray.length > 0) {\n                            // Prendre 10 échantillons répartis sur le spectre\n                            const newBars = [];\n                            const step = Math.floor(dataArray.length / 10);\n                            for(let i = 0; i < 10; i++){\n                                const index = i * step;\n                                const value = dataArray[index] || 0;\n                                // Convertir de 0-255 à 0-100\n                                newBars.push(value / 255 * 100);\n                            }\n                            setBars(newBars);\n                        } else {\n                            // Fallback vers simulation si pas de données\n                            setBars({\n                                \"AudioVisualizer.useEffect.updateBars\": (prev)=>prev.map({\n                                        \"AudioVisualizer.useEffect.updateBars\": ()=>Math.random() * 100\n                                    }[\"AudioVisualizer.useEffect.updateBars\"])\n                            }[\"AudioVisualizer.useEffect.updateBars\"]);\n                        }\n                    } else {\n                        setBars(new Array(10).fill(0));\n                    }\n                    if (isActive) {\n                        animationRef.current = requestAnimationFrame(updateBars);\n                    }\n                }\n            }[\"AudioVisualizer.useEffect.updateBars\"];\n            if (isActive) {\n                updateBars();\n            } else {\n                setBars(new Array(10).fill(0));\n            }\n            return ({\n                \"AudioVisualizer.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                }\n            })[\"AudioVisualizer.useEffect\"];\n        }\n    }[\"AudioVisualizer.useEffect\"], [\n        isActive,\n        getAnalyserData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-32 flex items-end justify-center space-x-2 bg-gray-800 rounded p-4\",\n        children: bars.map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 bg-blue-500 rounded-t\",\n                style: {\n                    height: \"\".concat(Math.max(height, 5), \"%\")\n                }\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Every\\\\fp\\\\egaliseur\\\\src\\\\components\\\\AudioVisualizer.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioVisualizer, \"EJ5bYlVKxMyIa6+i7EDrM58HrP0=\");\n_c = AudioVisualizer;\nvar _c;\n$RefreshReg$(_c, \"AudioVisualizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioVisualizer.tsx\n"));

/***/ })

});