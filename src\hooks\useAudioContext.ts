'use client'

import { useState, useRef, useCallback } from 'react'

interface AudioContextState {
  audioContext: AudioContext | null
  gainNode: GainNode | null
  analyserNode: AnalyserNode | null
  eqNodes: BiquadFilterNode[]
  isInitialized: boolean
  volume: number
  frequencies: { [key: string]: number }
}

export const useAudioContext = () => {
  const [state, setState] = useState<AudioContextState>({
    audioContext: null,
    gainNode: null,
    analyserNode: null,
    eqNodes: [],
    isInitialized: false,
    volume: 75,
    frequencies: {
      '32Hz': 0,
      '64Hz': 0,
      '125Hz': 0,
      '250Hz': 0,
      '500Hz': 0,
      '1kHz': 0,
      '2kHz': 0,
      '4kHz': 0,
      '8kHz': 0,
      '16kHz': 0
    }
  })

  const sourceNodeRef = useRef<MediaElementAudioSourceNode | null>(null)
  const oscillatorRef = useRef<OscillatorNode | null>(null)

  // Fréquences de l'égaliseur
  const eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]

  const initializeAudioContext = useCallback(async () => {
    if (state.isInitialized) return

    try {
      console.log('Initializing audio context...')

      // Créer le contexte audio
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Reprendre le contexte si suspendu
      if (audioContext.state === 'suspended') {
        await audioContext.resume()
      }

      // Créer les nœuds
      const gainNode = audioContext.createGain()
      const analyserNode = audioContext.createAnalyser()

      // Créer les filtres d'égaliseur
      const eqNodes = eqFrequencies.map((freq, index) => {
        const filter = audioContext.createBiquadFilter()
        filter.type = index === 0 ? 'lowshelf' :
                     index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking'
        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0
        return filter
      })

      // Connecter les nœuds en chaîne
      let previousNode: AudioNode = gainNode
      eqNodes.forEach(node => {
        previousNode.connect(node)
        previousNode = node
      })
      previousNode.connect(analyserNode)
      analyserNode.connect(audioContext.destination)

      // Configurer l'analyseur
      analyserNode.fftSize = 256
      analyserNode.smoothingTimeConstant = 0.8

      // Définir le volume initial
      gainNode.gain.value = 0.75

      setState(prev => ({
        ...prev,
        audioContext,
        gainNode,
        analyserNode,
        eqNodes,
        isInitialized: true
      }))

      console.log('Audio context initialized successfully', {
        state: audioContext.state,
        sampleRate: audioContext.sampleRate
      })
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
    }
  }, [state.isInitialized])

  const connectAudioElement = useCallback((audioElement: HTMLAudioElement) => {
    if (!state.audioContext || !state.gainNode) return

    try {
      // Déconnecter l'ancien source si il existe
      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect()
      }

      // Créer un nouveau source node
      const sourceNode = state.audioContext.createMediaElementSource(audioElement)
      sourceNode.connect(state.gainNode)

      sourceNodeRef.current = sourceNode
      audioElementRef.current = audioElement

      console.log('Audio element connected')
    } catch (error) {
      console.error('Failed to connect audio element:', error)
    }
  }, [state.audioContext, state.gainNode])

  const setVolume = useCallback((volume: number) => {
    console.log('Setting volume to:', volume)

    if (!state.gainNode || !state.audioContext) {
      console.log('No gain node or audio context available')
      return
    }

    const clampedVolume = Math.max(0, Math.min(100, volume))
    const gainValue = clampedVolume / 100

    try {
      state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime)
      console.log('Volume set successfully:', gainValue)
    } catch (error) {
      console.error('Error setting volume:', error)
    }

    setState(prev => ({
      ...prev,
      volume: clampedVolume
    }))
  }, [state.gainNode, state.audioContext])

  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    console.log('Setting EQ frequency:', freqKey, 'to gain:', gain)

    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1 || !state.eqNodes[freqIndex] || !state.audioContext) {
      console.log('EQ node not found or audio context not available')
      return
    }

    const clampedGain = Math.max(-10, Math.min(10, gain))
    const eqNode = state.eqNodes[freqIndex]

    try {
      eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime)
      console.log('EQ frequency set successfully:', freqKey, clampedGain)
    } catch (error) {
      console.error('Error setting EQ frequency:', error)
    }

    setState(prev => ({
      ...prev,
      frequencies: {
        ...prev.frequencies,
        [freqKey]: clampedGain
      }
    }))
  }, [state.eqNodes, state.frequencies, state.audioContext])

  const resetEQ = useCallback(() => {
    state.eqNodes.forEach(node => {
      node.gain.setValueAtTime(0, state.audioContext!.currentTime)
    })

    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number })
    }))
  }, [state.eqNodes, state.audioContext])

  const getAnalyserData = useCallback(() => {
    if (!state.analyserNode) return new Uint8Array(0)

    const bufferLength = state.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    state.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }, [state.analyserNode])

  const playTestTone = useCallback(async () => {
    console.log('Playing test tone...')

    if (!state.audioContext || !state.gainNode) {
      console.log('Initializing audio context first...')
      await initializeAudioContext()
      return
    }

    try {
      // Arrêter l'oscillateur précédent s'il existe
      if (oscillatorRef.current) {
        oscillatorRef.current.stop()
        oscillatorRef.current = null
      }

      // Créer un nouvel oscillateur
      const oscillator = state.audioContext.createOscillator()
      oscillator.type = 'sine'
      oscillator.frequency.setValueAtTime(440, state.audioContext.currentTime) // La note A4

      // Connecter l'oscillateur à la chaîne audio
      oscillator.connect(state.gainNode)

      // Démarrer et arrêter après 2 secondes
      oscillator.start()
      oscillator.stop(state.audioContext.currentTime + 2)

      oscillatorRef.current = oscillator

      console.log('Test tone playing for 2 seconds')
    } catch (error) {
      console.error('Error playing test tone:', error)
    }
  }, [state.audioContext, state.gainNode, initializeAudioContext])

  return {
    ...state,
    initializeAudioContext,
    connectAudioElement,
    setVolume,
    setEQFrequency,
    resetEQ,
    getAnalyserData,
    playTestTone
  }
}
