'use client'

import { useState, useRef, useCallback, useEffect } from 'react'

interface AudioState {
  isInitialized: boolean
  isPlaying: boolean
  volume: number
  frequencies: { [key: string]: number }
  currentPreset: string
  error: string | null
}

class AudioEngine {
  private audioContext: AudioContext | null = null
  private gainNode: GainNode | null = null
  private analyserNode: AnalyserNode | null = null
  private eqNodes: BiquadFilterNode[] = []
  private oscillator: OscillatorNode | null = null
  private sourceNode: MediaElementAudioSourceNode | null = null

  private readonly eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]

  async initialize(): Promise<boolean> {
    try {
      console.log('🎵 Initializing AudioEngine...')

      // Créer le contexte audio
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Reprendre le contexte si suspendu
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      // Créer les nœuds audio
      this.gainNode = this.audioContext.createGain()
      this.analyserNode = this.audioContext.createAnalyser()

      // Créer les filtres d'égaliseur
      this.eqNodes = this.eqFrequencies.map((freq, index) => {
        const filter = this.audioContext!.createBiquadFilter()

        if (index === 0) {
          filter.type = 'lowshelf'
        } else if (index === this.eqFrequencies.length - 1) {
          filter.type = 'highshelf'
        } else {
          filter.type = 'peaking'
        }

        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0

        return filter
      })

      // Connecter la chaîne audio
      this.connectAudioChain()

      // Configurer l'analyseur
      this.analyserNode.fftSize = 256
      this.analyserNode.smoothingTimeConstant = 0.8

      // Volume initial
      this.gainNode.gain.value = 0.75

      console.log('✅ AudioEngine initialized successfully')
      return true

    } catch (error) {
      console.error('❌ Failed to initialize AudioEngine:', error)
      return false
    }
  }

  private connectAudioChain() {
    if (!this.gainNode || !this.analyserNode) return

    // Connecter : source → gain → eq1 → eq2 → ... → analyser → destination
    let currentNode: AudioNode = this.gainNode

    this.eqNodes.forEach(eqNode => {
      currentNode.connect(eqNode)
      currentNode = eqNode
    })

    currentNode.connect(this.analyserNode)
    this.analyserNode.connect(this.audioContext!.destination)
  }

  async playTestTone(frequency: number = 440, duration: number = 2): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('AudioEngine not initialized')
    }

    try {
      // Arrêter l'oscillateur précédent
      this.stopTestTone()

      // Créer un nouvel oscillateur
      this.oscillator = this.audioContext.createOscillator()
      this.oscillator.type = 'sine'
      this.oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime)

      // Connecter à la chaîne audio
      this.oscillator.connect(this.gainNode)

      // Démarrer et programmer l'arrêt
      this.oscillator.start()
      this.oscillator.stop(this.audioContext.currentTime + duration)

      console.log(`🔊 Playing test tone: ${frequency}Hz for ${duration}s`)

    } catch (error) {
      console.error('❌ Error playing test tone:', error)
      throw error
    }
  }

  stopTestTone() {
    if (this.oscillator) {
      try {
        this.oscillator.stop()
      } catch (e) {
        // Ignore si déjà arrêté
      }
      this.oscillator = null
    }
  }

  setVolume(volume: number) {
    if (!this.gainNode || !this.audioContext) return

    const clampedVolume = Math.max(0, Math.min(100, volume))
    const gainValue = clampedVolume / 100

    this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime)
    console.log(`🔊 Volume set to: ${clampedVolume}% (gain: ${gainValue})`)
  }

  setEQBand(bandIndex: number, gain: number) {
    if (!this.eqNodes[bandIndex] || !this.audioContext) return

    const clampedGain = Math.max(-12, Math.min(12, gain))
    this.eqNodes[bandIndex].gain.setValueAtTime(clampedGain, this.audioContext.currentTime)

    const freq = this.eqFrequencies[bandIndex]
    console.log(`🎛️ EQ ${freq}Hz set to: ${clampedGain}dB`)
  }

  resetEQ() {
    this.eqNodes.forEach((node, index) => {
      if (this.audioContext) {
        node.gain.setValueAtTime(0, this.audioContext.currentTime)
      }
    })
    console.log('🔄 EQ reset to flat')
  }

  getAnalyserData(): Uint8Array {
    if (!this.analyserNode) return new Uint8Array(0)

    const bufferLength = this.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }

  connectAudioElement(audioElement: HTMLAudioElement) {
    if (!this.audioContext || !this.gainNode) return

    try {
      // Déconnecter l'ancien source
      if (this.sourceNode) {
        this.sourceNode.disconnect()
      }

      // Créer et connecter le nouveau source
      this.sourceNode = this.audioContext.createMediaElementSource(audioElement)
      this.sourceNode.connect(this.gainNode)

      console.log('🎵 Audio element connected')
    } catch (error) {
      console.error('❌ Error connecting audio element:', error)
    }
  }

  destroy() {
    this.stopTestTone()

    if (this.sourceNode) {
      this.sourceNode.disconnect()
    }

    if (this.audioContext) {
      this.audioContext.close()
    }

    console.log('🗑️ AudioEngine destroyed')
  }
}

export const useAudioContext = () => {
  const [state, setState] = useState<AudioState>({
    isInitialized: false,
    isPlaying: false,
    volume: 75,
    frequencies: {
      '32Hz': 0,
      '64Hz': 0,
      '125Hz': 0,
      '250Hz': 0,
      '500Hz': 0,
      '1kHz': 0,
      '2kHz': 0,
      '4kHz': 0,
      '8kHz': 0,
      '16kHz': 0
    },
    currentPreset: 'flat',
    error: null
  })

  const audioEngineRef = useRef<AudioEngine | null>(null)
  const playTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const initializeAudioContext = useCallback(async () => {
    if (state.isInitialized) return

    try {
      if (!audioEngineRef.current) {
        audioEngineRef.current = new AudioEngine()
      }

      const success = await audioEngineRef.current.initialize()

      setState(prev => ({
        ...prev,
        isInitialized: success,
        error: success ? null : 'Failed to initialize audio context'
      }))

    } catch (error) {
      console.error('❌ Failed to initialize audio context:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [state.isInitialized])

  const setVolume = useCallback((volume: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return

    audioEngineRef.current.setVolume(volume)
    setState(prev => ({ ...prev, volume }))
  }, [state.isInitialized])

  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    if (!audioEngineRef.current || !state.isInitialized) return

    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1) return

    audioEngineRef.current.setEQBand(freqIndex, gain)
    setState(prev => ({
      ...prev,
      frequencies: { ...prev.frequencies, [freqKey]: gain }
    }))
  }, [state.isInitialized, state.frequencies])

  const resetEQ = useCallback(() => {
    if (!audioEngineRef.current || !state.isInitialized) return

    audioEngineRef.current.resetEQ()
    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number }),
      currentPreset: 'flat'
    }))
  }, [state.isInitialized])
        eqNodes,
        isInitialized: true
      }))

      console.log('Audio context initialized successfully', {
        state: audioContext.state,
        sampleRate: audioContext.sampleRate
      })
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
    }
  }, [state.isInitialized])

  const connectAudioElement = useCallback((audioElement: HTMLAudioElement) => {
    if (!state.audioContext || !state.gainNode) return

    try {
      // Déconnecter l'ancien source si il existe
      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect()
      }

      // Créer un nouveau source node
      const sourceNode = state.audioContext.createMediaElementSource(audioElement)
      sourceNode.connect(state.gainNode)

      sourceNodeRef.current = sourceNode
      audioElementRef.current = audioElement

      console.log('Audio element connected')
    } catch (error) {
      console.error('Failed to connect audio element:', error)
    }
  }, [state.audioContext, state.gainNode])

  const setVolume = useCallback((volume: number) => {
    console.log('Setting volume to:', volume)

    if (!state.gainNode || !state.audioContext) {
      console.log('No gain node or audio context available')
      return
    }

    const clampedVolume = Math.max(0, Math.min(100, volume))
    const gainValue = clampedVolume / 100

    try {
      state.gainNode.gain.setValueAtTime(gainValue, state.audioContext.currentTime)
      console.log('Volume set successfully:', gainValue)
    } catch (error) {
      console.error('Error setting volume:', error)
    }

    setState(prev => ({
      ...prev,
      volume: clampedVolume
    }))
  }, [state.gainNode, state.audioContext])

  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    console.log('Setting EQ frequency:', freqKey, 'to gain:', gain)

    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1 || !state.eqNodes[freqIndex] || !state.audioContext) {
      console.log('EQ node not found or audio context not available')
      return
    }

    const clampedGain = Math.max(-10, Math.min(10, gain))
    const eqNode = state.eqNodes[freqIndex]

    try {
      eqNode.gain.setValueAtTime(clampedGain, state.audioContext.currentTime)
      console.log('EQ frequency set successfully:', freqKey, clampedGain)
    } catch (error) {
      console.error('Error setting EQ frequency:', error)
    }

    setState(prev => ({
      ...prev,
      frequencies: {
        ...prev.frequencies,
        [freqKey]: clampedGain
      }
    }))
  }, [state.eqNodes, state.frequencies, state.audioContext])

  const resetEQ = useCallback(() => {
    state.eqNodes.forEach(node => {
      node.gain.setValueAtTime(0, state.audioContext!.currentTime)
    })

    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number })
    }))
  }, [state.eqNodes, state.audioContext])

  const getAnalyserData = useCallback(() => {
    if (!state.analyserNode) return new Uint8Array(0)

    const bufferLength = state.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    state.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }, [state.analyserNode])

  const playTestTone = useCallback(async () => {
    console.log('Playing test tone...')

    if (!state.audioContext || !state.gainNode) {
      console.log('Initializing audio context first...')
      await initializeAudioContext()
      return
    }

    try {
      // Arrêter l'oscillateur précédent s'il existe
      if (oscillatorRef.current) {
        oscillatorRef.current.stop()
        oscillatorRef.current = null
      }

      // Créer un nouvel oscillateur
      const oscillator = state.audioContext.createOscillator()
      oscillator.type = 'sine'
      oscillator.frequency.setValueAtTime(440, state.audioContext.currentTime) // La note A4

      // Connecter l'oscillateur à la chaîne audio
      oscillator.connect(state.gainNode)

      // Démarrer et arrêter après 2 secondes
      oscillator.start()
      oscillator.stop(state.audioContext.currentTime + 2)

      oscillatorRef.current = oscillator

      console.log('Test tone playing for 2 seconds')
    } catch (error) {
      console.error('Error playing test tone:', error)
    }
  }, [state.audioContext, state.gainNode, initializeAudioContext])

  return {
    ...state,
    initializeAudioContext,
    connectAudioElement,
    setVolume,
    setEQFrequency,
    resetEQ,
    getAnalyserData,
    playTestTone
  }
}
