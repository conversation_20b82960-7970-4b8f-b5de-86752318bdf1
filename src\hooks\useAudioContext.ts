'use client'

import { useState, useEffect, useRef, useCallback } from 'react'

interface AudioContextState {
  audioContext: AudioContext | null
  gainNode: GainNode | null
  analyserNode: AnalyserNode | null
  eqNodes: BiquadFilterNode[]
  isInitialized: boolean
  volume: number
  frequencies: { [key: string]: number }
}

export const useAudioContext = () => {
  const [state, setState] = useState<AudioContextState>({
    audioContext: null,
    gainNode: null,
    analyserNode: null,
    eqNodes: [],
    isInitialized: false,
    volume: 75,
    frequencies: {
      '32Hz': 0,
      '64Hz': 0,
      '125Hz': 0,
      '250Hz': 0,
      '500Hz': 0,
      '1kHz': 0,
      '2kHz': 0,
      '4kHz': 0,
      '8kHz': 0,
      '16kHz': 0
    }
  })

  const sourceNodeRef = useRef<MediaElementAudioSourceNode | null>(null)
  const audioElementRef = useRef<HTMLAudioElement | null>(null)

  // Fréquences de l'égaliseur
  const eqFrequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]

  const initializeAudioContext = useCallback(async () => {
    try {
      // Créer le contexte audio
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // Créer les nœuds
      const gainNode = audioContext.createGain()
      const analyserNode = audioContext.createAnalyser()
      
      // Créer les filtres d'égaliseur
      const eqNodes = eqFrequencies.map((freq, index) => {
        const filter = audioContext.createBiquadFilter()
        filter.type = index === 0 ? 'lowshelf' : 
                     index === eqFrequencies.length - 1 ? 'highshelf' : 'peaking'
        filter.frequency.value = freq
        filter.Q.value = 1
        filter.gain.value = 0
        return filter
      })

      // Connecter les nœuds en chaîne
      let previousNode: AudioNode = gainNode
      eqNodes.forEach(node => {
        previousNode.connect(node)
        previousNode = node
      })
      previousNode.connect(analyserNode)
      analyserNode.connect(audioContext.destination)

      // Configurer l'analyseur
      analyserNode.fftSize = 256
      analyserNode.smoothingTimeConstant = 0.8

      // Définir le volume initial
      gainNode.gain.value = 0.75

      setState(prev => ({
        ...prev,
        audioContext,
        gainNode,
        analyserNode,
        eqNodes,
        isInitialized: true
      }))

      console.log('Audio context initialized successfully')
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
    }
  }, [])

  const connectAudioElement = useCallback((audioElement: HTMLAudioElement) => {
    if (!state.audioContext || !state.gainNode) return

    try {
      // Déconnecter l'ancien source si il existe
      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect()
      }

      // Créer un nouveau source node
      const sourceNode = state.audioContext.createMediaElementSource(audioElement)
      sourceNode.connect(state.gainNode)
      
      sourceNodeRef.current = sourceNode
      audioElementRef.current = audioElement

      console.log('Audio element connected')
    } catch (error) {
      console.error('Failed to connect audio element:', error)
    }
  }, [state.audioContext, state.gainNode])

  const setVolume = useCallback((volume: number) => {
    if (!state.gainNode) return

    const clampedVolume = Math.max(0, Math.min(100, volume))
    const gainValue = clampedVolume / 100
    
    state.gainNode.gain.setValueAtTime(gainValue, state.audioContext!.currentTime)
    
    setState(prev => ({
      ...prev,
      volume: clampedVolume
    }))
  }, [state.gainNode, state.audioContext])

  const setEQFrequency = useCallback((freqKey: string, gain: number) => {
    const freqIndex = Object.keys(state.frequencies).indexOf(freqKey)
    if (freqIndex === -1 || !state.eqNodes[freqIndex]) return

    const clampedGain = Math.max(-10, Math.min(10, gain))
    const eqNode = state.eqNodes[freqIndex]
    
    eqNode.gain.setValueAtTime(clampedGain, state.audioContext!.currentTime)
    
    setState(prev => ({
      ...prev,
      frequencies: {
        ...prev.frequencies,
        [freqKey]: clampedGain
      }
    }))
  }, [state.eqNodes, state.frequencies, state.audioContext])

  const resetEQ = useCallback(() => {
    state.eqNodes.forEach(node => {
      node.gain.setValueAtTime(0, state.audioContext!.currentTime)
    })

    setState(prev => ({
      ...prev,
      frequencies: Object.keys(prev.frequencies).reduce((acc, key) => {
        acc[key] = 0
        return acc
      }, {} as { [key: string]: number })
    }))
  }, [state.eqNodes, state.audioContext])

  const getAnalyserData = useCallback(() => {
    if (!state.analyserNode) return new Uint8Array(0)

    const bufferLength = state.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    state.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }, [state.analyserNode])

  // Initialiser automatiquement au montage
  useEffect(() => {
    initializeAudioContext()
  }, [initializeAudioContext])

  return {
    ...state,
    initializeAudioContext,
    connectAudioElement,
    setVolume,
    setEQFrequency,
    resetEQ,
    getAnalyserData
  }
}
