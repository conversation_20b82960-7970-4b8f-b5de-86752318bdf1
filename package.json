{"name": "wavecraft", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": ["equalizer", "ai", "audio", "music"], "author": "", "license": "ISC", "description": "WaveCraft - Égaliseur audio intelligent basé sur l'analyse sonore en temps réel par IA", "dependencies": {"@types/node": "^22.15.21", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "next": "^15.3.2", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}